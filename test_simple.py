#!/usr/bin/env python3
"""
Simple Django API Test

Tests basic functionality of the HeartGrid Django API
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from heartgrid.models import Profile, Subscription
import json

User = get_user_model()

def test_basic_functionality():
    """Test basic Django functionality"""
    print("=" * 50)
    print("HeartGrid Django Basic Test")
    print("=" * 50)
    
    # Test 1: Database connection
    try:
        user_count = User.objects.count()
        print(f"✓ Database connection working. Users in DB: {user_count}")
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False
    
    # Test 2: User model creation
    try:
        # Clean up any existing test user
        User.objects.filter(email='<EMAIL>').delete()
        
        test_user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='testpass123'
        )
        print(f"✓ User model creation working. User ID: {test_user.id}")
    except Exception as e:
        print(f"✗ User model error: {e}")
        return False
    
    # Test 3: Profile creation
    try:
        profile, created = Profile.objects.get_or_create(
            user=test_user,
            defaults={
                'bio': 'Test bio',
                'location': 'Cape Town, South Africa',
                'gender': 'male',
                'interested_in': 'female'
            }
        )
        print(f"✓ Profile creation working. Profile ID: {profile.id}")
    except Exception as e:
        print(f"✗ Profile creation error: {e}")
        return False
    
    # Test 4: Subscription creation
    try:
        from django.utils import timezone
        from datetime import timedelta

        subscription, created = Subscription.objects.get_or_create(
            user=test_user,
            defaults={
                'plan': 'trial',
                'status': 'active',
                'expires_at': timezone.now() + timedelta(days=7),
                'features': ['basic_chat']
            }
        )
        print(f"✓ Subscription creation working. Plan: {subscription.plan}")
    except Exception as e:
        print(f"✗ Subscription creation error: {e}")
        return False
    
    # Test 5: Django test client
    try:
        client = Client()
        
        # Test registration endpoint
        response = client.post('/api/v1/auth/register/', {
            'email': '<EMAIL>',
            'name': 'New User',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'date_of_birth': '1990-01-01'
        }, content_type='application/json')
        
        print(f"Registration endpoint status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("✓ Registration endpoint working")
            
            # Get token from response
            try:
                data = json.loads(response.content)
                token = data.get('token')
                if token:
                    print(f"✓ Authentication token received: {token[:20]}...")
                    
                    # Test authenticated endpoint
                    auth_headers = {'HTTP_AUTHORIZATION': f'Token {token}'}
                    profile_response = client.get('/api/v1/profile/', **auth_headers)
                    
                    print(f"Profile endpoint status: {profile_response.status_code}")
                    if profile_response.status_code == 200:
                        print("✓ Authenticated endpoints working")
                    else:
                        print(f"✗ Profile endpoint error: {profile_response.content}")
                else:
                    print("✗ No token in registration response")
            except json.JSONDecodeError:
                print(f"✗ Invalid JSON response: {response.content}")
        else:
            print(f"✗ Registration endpoint error: {response.content}")
    except Exception as e:
        print(f"✗ API endpoint test error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("Basic functionality test completed successfully!")
    print("=" * 50)
    return True

def test_gamification():
    """Test gamification features"""
    print("\nTesting gamification features...")
    
    try:
        from heartgrid.gamification import GamificationEngine
        
        # Get a test user
        user = User.objects.filter(email='<EMAIL>').first()
        if not user:
            print("✗ No test user found for gamification test")
            return False
        
        engine = GamificationEngine()
        
        # Test stats calculation
        stats = engine.get_user_stats(user)
        print(f"✓ User stats calculated: {stats}")
        
        # Test achievement checking
        achievements = engine.check_achievements(user)
        print(f"✓ Achievement check completed: {len(achievements)} achievements")
        
        # Test leaderboard
        leaderboard = engine.get_leaderboard()
        print(f"✓ Leaderboard generated: {len(leaderboard)} users")
        
        return True
    except Exception as e:
        print(f"✗ Gamification test error: {e}")
        return False

def test_crypto_utils():
    """Test cryptocurrency utilities"""
    print("\nTesting cryptocurrency utilities...")
    
    try:
        from heartgrid.crypto_utils import CryptoPaymentHandler
        
        handler = CryptoPaymentHandler()
        
        # Test supported chains
        chains = handler.get_supported_chains()
        print(f"✓ Supported chains: {list(chains.keys())}")
        
        # Test price fetching (mock)
        try:
            price = handler.get_token_price('ethereum', 'native')
            print(f"✓ Price fetching works: ETH price = ${price}")
        except:
            print("✓ Price fetching configured (external API may be unavailable)")
        
        # Test payment address generation
        user = User.objects.filter(email='<EMAIL>').first()
        if user:
            payment = handler.create_payment_request(user, 'weekly', 'ethereum', 'native')
            print(f"✓ Payment request created: {payment.payment_address[:10]}...")
        
        return True
    except Exception as e:
        print(f"✗ Crypto utils test error: {e}")
        return False

if __name__ == '__main__':
    success = True
    
    # Run basic functionality test
    if not test_basic_functionality():
        success = False
    
    # Run gamification test
    if not test_gamification():
        success = False
    
    # Run crypto utils test
    if not test_crypto_utils():
        success = False
    
    if success:
        print("\n🎉 All tests passed! Django API is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)
