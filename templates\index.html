{% extends "base.html" %}

{% block content %}
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        Find Your Perfect Match on
                        <span class="text-primary">HeartGrid</span>
                        <i class="fas fa-heart text-danger ms-2 heartbeat"></i>
                    </h1>
                    <p class="lead mb-4 text-muted">
                        Discover meaningful connections through our unique grid-based matching system. 
                        Swipe, match, and chat with people who share your interests and values.
                    </p>
                    <div class="hero-buttons">
                        <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg me-3 mb-3">
                            <i class="fas fa-heart me-2"></i>
                            Start Dating
                        </a>
                        <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-lg mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Sign In
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="floating-hearts">
                        <i class="fas fa-heart text-danger"></i>
                        <i class="fas fa-heart text-primary"></i>
                        <i class="fas fa-heart text-warning"></i>
                        <i class="fas fa-heart text-success"></i>
                        <i class="fas fa-heart text-info"></i>
                    </div>
                    <img src="https://pixabay.com/get/g63d3a37c4bae0346ec2715bae133406d8315013c717d2fc73249f8231b57df1bf1f9f0f497640d45e60dd345418b54fec7ad3016bce14d3c75d4f1dd0fe7fb91_1280.jpg" 
                         alt="Happy Couple" class="img-fluid rounded-lg shadow-lg">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">Why Choose HeartGrid?</h2>
                <p class="lead text-muted">Experience dating in a whole new way with our innovative features</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-th-large fa-3x text-primary"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Grid Discovery</h4>
                    <p class="text-muted">Browse profiles in an intuitive grid layout. See more potential matches at once and make better choices.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-heart fa-3x text-danger"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Smart Matching</h4>
                    <p class="text-muted">Our algorithm considers your preferences, interests, and location to find your perfect match.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-comments fa-3x text-success"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Instant Chat</h4>
                    <p class="text-muted">When you both like each other, start chatting immediately. Break the ice and get to know each other.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Success Stories Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">Success Stories</h2>
                <p class="lead text-muted">Real couples who found love on HeartGrid</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="story-card">
                    <img src="https://pixabay.com/get/g3f3b978520b888bf29d7074120c15fefceff88c3187b45998d18c88da33d8a0242cfd0964bab07586db3afb3ef8ecbbdc18acd6a3ddd7235509155ecb1810424_1280.jpg" 
                         alt="Happy Couple" class="story-image">
                    <div class="story-content">
                        <h5 class="fw-bold mb-2">"We found each other!"</h5>
                        <p class="text-muted">"HeartGrid's grid layout helped us discover each other when traditional swiping apps failed. We've been together for 2 years now!"</p>
                        <div class="story-hearts">
                            <i class="fas fa-heart text-danger"></i>
                            <i class="fas fa-heart text-danger"></i>
                            <i class="fas fa-heart text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="story-card">
                    <img src="https://pixabay.com/get/g7e87443f4f0f94c04f0001197b15b09f20ac55cabf0fad06134dcdf785e08a2bf6a4a9241602fac5e1368587f937d98dbc8e0c15107f456a0fbc3d8b20ca9d6d_1280.jpg" 
                         alt="Romantic Couple" class="story-image">
                    <div class="story-content">
                        <h5 class="fw-bold mb-2">"Love at first match!"</h5>
                        <p class="text-muted">"The unique grid interface made browsing so much more enjoyable. We matched on our first day and haven't looked back since!"</p>
                        <div class="story-hearts">
                            <i class="fas fa-heart text-danger"></i>
                            <i class="fas fa-heart text-danger"></i>
                            <i class="fas fa-heart text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container text-center">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">Ready to Find Your Perfect Match?</h2>
                <p class="lead mb-4">Join thousands of singles who have found love on HeartGrid. Your story could be next!</p>
                <a href="{{ url_for('register') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-heart me-2"></i>
                    Join HeartGrid Now
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
