"""
Push Notification System for HeartGrid
Real-time engagement notifications for web and mobile
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import current_app

class NotificationManager:
    """Manages push notifications and real-time alerts"""
    
    def __init__(self, data_store):
        self.data_store = data_store
        
        # Initialize notification storage if not exists
        if not hasattr(data_store, 'notifications'):
            data_store.notifications = {}
        
        if not hasattr(data_store, 'notification_preferences'):
            data_store.notification_preferences = {}
        
        # Notification types and their default settings
        self.notification_types = {
            'new_match': {
                'title': 'New Match!',
                'default_enabled': True,
                'importance': 'high',
                'sound': True
            },
            'new_message': {
                'title': 'New Message',
                'default_enabled': True,
                'importance': 'high',
                'sound': True
            },
            'new_like': {
                'title': 'Someone Liked You!',
                'default_enabled': True,
                'importance': 'medium',
                'sound': False
            },
            'super_like_received': {
                'title': 'Super Like Received!',
                'default_enabled': True,
                'importance': 'high',
                'sound': True
            },
            'profile_view': {
                'title': 'Profile View',
                'default_enabled': False,
                'importance': 'low',
                'sound': False
            },
            'daily_challenge': {
                'title': 'Daily Challenge',
                'default_enabled': True,
                'importance': 'medium',
                'sound': False
            },
            'achievement_unlocked': {
                'title': 'Achievement Unlocked!',
                'default_enabled': True,
                'importance': 'medium',
                'sound': True
            },
            'subscription_reminder': {
                'title': 'Subscription Reminder',
                'default_enabled': True,
                'importance': 'medium',
                'sound': False
            },
            'boost_expired': {
                'title': 'Profile Boost Expired',
                'default_enabled': True,
                'importance': 'low',
                'sound': False
            },
            'login_streak': {
                'title': 'Login Streak',
                'default_enabled': True,
                'importance': 'low',
                'sound': False
            }
        }
    
    def get_user_preferences(self, user_id: str) -> Dict:
        """Get user's notification preferences"""
        if user_id not in self.data_store.notification_preferences:
            # Set default preferences
            self.data_store.notification_preferences[user_id] = {
                notification_type: config['default_enabled']
                for notification_type, config in self.notification_types.items()
            }
            # Add general settings
            self.data_store.notification_preferences[user_id].update({
                'push_enabled': True,
                'email_enabled': True,
                'quiet_hours_start': '22:00',
                'quiet_hours_end': '08:00',
                'weekend_only': False
            })
        
        return self.data_store.notification_preferences[user_id]
    
    def update_user_preferences(self, user_id: str, preferences: Dict):
        """Update user's notification preferences"""
        current_prefs = self.get_user_preferences(user_id)
        current_prefs.update(preferences)
    
    def send_notification(self, user_id: str, notification_type: str, data: Dict) -> bool:
        """Send notification to user"""
        
        # Check if user has notifications enabled for this type
        preferences = self.get_user_preferences(user_id)
        if not preferences.get(notification_type, False):
            return False
        
        # Check quiet hours
        if self._is_quiet_hours(preferences):
            # Store for later delivery
            self._queue_notification(user_id, notification_type, data)
            return False
        
        # Create notification
        notification = self._create_notification(user_id, notification_type, data)
        
        # Store notification
        self._store_notification(user_id, notification)
        
        # Send push notification (web push API simulation)
        self._send_push_notification(user_id, notification)
        
        return True
    
    def _create_notification(self, user_id: str, notification_type: str, data: Dict) -> Dict:
        """Create notification object"""
        config = self.notification_types[notification_type]
        
        notification = {
            'id': f"{user_id}_{notification_type}_{datetime.now().timestamp()}",
            'user_id': user_id,
            'type': notification_type,
            'title': data.get('title', config['title']),
            'message': data['message'],
            'data': data.get('data', {}),
            'created_at': datetime.now().isoformat(),
            'read': False,
            'importance': config['importance'],
            'sound': config['sound']
        }
        
        return notification
    
    def _store_notification(self, user_id: str, notification: Dict):
        """Store notification in user's notification list"""
        if user_id not in self.data_store.notifications:
            self.data_store.notifications[user_id] = []
        
        self.data_store.notifications[user_id].append(notification)
        
        # Keep only last 50 notifications
        if len(self.data_store.notifications[user_id]) > 50:
            self.data_store.notifications[user_id] = self.data_store.notifications[user_id][-50:]
    
    def _send_push_notification(self, user_id: str, notification: Dict):
        """Send actual push notification (simulation)"""
        # In a real implementation, this would integrate with:
        # - Web Push API for web browsers
        # - FCM for Android
        # - APNs for iOS
        
        push_payload = {
            'title': notification['title'],
            'body': notification['message'],
            'icon': '/static/img/heartgrid-icon.png',
            'badge': '/static/img/heartgrid-badge.png',
            'data': {
                'notification_id': notification['id'],
                'type': notification['type'],
                'user_id': user_id,
                'url': self._get_notification_url(notification)
            },
            'requireInteraction': notification['importance'] == 'high',
            'silent': not notification['sound']
        }
        
        # Log notification for debugging
        print(f"Push notification sent to {user_id}: {notification['title']}")
        
        return push_payload
    
    def _get_notification_url(self, notification: Dict) -> str:
        """Get URL to navigate to when notification is clicked"""
        notification_type = notification['type']
        
        url_mapping = {
            'new_match': '/matches',
            'new_message': f"/chat/{notification['data'].get('sender_id', '')}",
            'new_like': '/discover',
            'super_like_received': '/discover',
            'profile_view': '/profile_analytics',
            'daily_challenge': '/gamification',
            'achievement_unlocked': '/gamification',
            'subscription_reminder': '/subscription',
            'boost_expired': '/profile',
            'login_streak': '/gamification'
        }
        
        return url_mapping.get(notification_type, '/')
    
    def _is_quiet_hours(self, preferences: Dict) -> bool:
        """Check if current time is within user's quiet hours"""
        if not preferences.get('push_enabled', True):
            return True
        
        now = datetime.now().time()
        start_time = datetime.strptime(preferences.get('quiet_hours_start', '22:00'), '%H:%M').time()
        end_time = datetime.strptime(preferences.get('quiet_hours_end', '08:00'), '%H:%M').time()
        
        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # Crosses midnight
            return now >= start_time or now <= end_time
    
    def _queue_notification(self, user_id: str, notification_type: str, data: Dict):
        """Queue notification for later delivery"""
        # In a real implementation, this would use a task queue
        pass
    
    def get_user_notifications(self, user_id: str, limit: int = 20, unread_only: bool = False) -> List[Dict]:
        """Get user's notifications"""
        notifications = self.data_store.notifications.get(user_id, [])
        
        if unread_only:
            notifications = [n for n in notifications if not n['read']]
        
        # Sort by creation time (newest first)
        notifications.sort(key=lambda x: x['created_at'], reverse=True)
        
        return notifications[:limit]
    
    def mark_notification_read(self, user_id: str, notification_id: str) -> bool:
        """Mark notification as read"""
        notifications = self.data_store.notifications.get(user_id, [])
        
        for notification in notifications:
            if notification['id'] == notification_id:
                notification['read'] = True
                return True
        
        return False
    
    def mark_all_notifications_read(self, user_id: str):
        """Mark all notifications as read"""
        notifications = self.data_store.notifications.get(user_id, [])
        
        for notification in notifications:
            notification['read'] = True
    
    def get_unread_count(self, user_id: str) -> int:
        """Get count of unread notifications"""
        notifications = self.data_store.notifications.get(user_id, [])
        return sum(1 for n in notifications if not n['read'])
    
    def send_match_notification(self, user_id: str, match_user_id: str):
        """Send new match notification"""
        match_user = self.data_store.get_user_by_id(match_user_id)
        match_name = match_user.get('name', 'Someone') if match_user else 'Someone'
        
        self.send_notification(user_id, 'new_match', {
            'message': f"You and {match_name} matched! Start chatting now.",
            'data': {'match_user_id': match_user_id}
        })
    
    def send_message_notification(self, user_id: str, sender_id: str, message_preview: str):
        """Send new message notification"""
        sender = self.data_store.get_user_by_id(sender_id)
        sender_name = sender.get('name', 'Someone') if sender else 'Someone'
        
        # Truncate message preview
        preview = message_preview[:50] + "..." if len(message_preview) > 50 else message_preview
        
        self.send_notification(user_id, 'new_message', {
            'message': f"{sender_name}: {preview}",
            'data': {'sender_id': sender_id}
        })
    
    def send_like_notification(self, user_id: str, liker_id: str, is_super_like: bool = False):
        """Send like/super like notification"""
        liker = self.data_store.get_user_by_id(liker_id)
        liker_name = liker.get('name', 'Someone') if liker else 'Someone'
        
        if is_super_like:
            self.send_notification(user_id, 'super_like_received', {
                'message': f"{liker_name} sent you a Super Like!",
                'data': {'liker_id': liker_id}
            })
        else:
            self.send_notification(user_id, 'new_like', {
                'message': f"{liker_name} liked your profile!",
                'data': {'liker_id': liker_id}
            })
    
    def send_achievement_notification(self, user_id: str, achievement_name: str, points: int):
        """Send achievement unlocked notification"""
        self.send_notification(user_id, 'achievement_unlocked', {
            'message': f"You unlocked '{achievement_name}'! +{points} points",
            'data': {'achievement': achievement_name, 'points': points}
        })
    
    def send_daily_challenge_notification(self, user_id: str, challenge_name: str):
        """Send daily challenge notification"""
        self.send_notification(user_id, 'daily_challenge', {
            'message': f"New daily challenge: {challenge_name}",
            'data': {'challenge': challenge_name}
        })
    
    def send_subscription_reminder(self, user_id: str, days_left: int):
        """Send subscription expiry reminder"""
        if days_left <= 0:
            message = "Your subscription has expired. Renew now to continue premium features!"
        elif days_left == 1:
            message = "Your subscription expires tomorrow! Renew now."
        else:
            message = f"Your subscription expires in {days_left} days. Renew now!"
        
        self.send_notification(user_id, 'subscription_reminder', {
            'message': message,
            'data': {'days_left': days_left}
        })
    
    def send_streak_notification(self, user_id: str, streak_count: int):
        """Send login streak notification"""
        if streak_count == 3:
            message = "3-day streak! You earned a bonus Super Like!"
        elif streak_count == 7:
            message = "Week-long streak! You earned a Profile Boost!"
        elif streak_count == 30:
            message = "30-day streak! You earned premium trial!"
        else:
            message = f"{streak_count}-day login streak! Keep it up!"
        
        self.send_notification(user_id, 'login_streak', {
            'message': message,
            'data': {'streak': streak_count}
        })

# Global notification manager
notification_manager = None

def init_notifications(data_store):
    global notification_manager
    notification_manager = NotificationManager(data_store)
    return notification_manager