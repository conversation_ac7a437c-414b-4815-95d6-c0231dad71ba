import json
import os
import requests
from flask import Blueprint, redirect, request, url_for, session, flash
from oauthlib.oauth2 import <PERSON>Application<PERSON>lient
from models import data_store
from datetime import date

GOOGLE_CLIENT_ID = os.environ["GOOGLE_OAUTH_CLIENT_ID"]
GOOGLE_CLIENT_SECRET = os.environ["GOOGLE_OAUTH_CLIENT_SECRET"]
GOOGLE_DISCOVERY_URL = "https://accounts.google.com/.well-known/openid-configuration"

# OAuth client setup
client = WebApplicationClient(GOOGLE_CLIENT_ID)

# Create blueprint for Google auth
google_auth = Blueprint("google_auth", __name__)

@google_auth.route("/google_login")
def google_login():
    # Get Google provider configuration
    google_provider_cfg = requests.get(GOOGLE_DISCOVERY_URL).json()
    authorization_endpoint = google_provider_cfg["authorization_endpoint"]

    # Request URI for Google OAuth
    request_uri = client.prepare_request_uri(
        authorization_endpoint,
        redirect_uri=request.base_url.replace("http://", "https://") + "_callback",
        scope=["openid", "email", "profile"],
    )
    return redirect(request_uri)

@google_auth.route("/google_login_callback")
def google_login_callback():
    # Get authorization code from Google
    code = request.args.get("code")
    
    # Get Google provider configuration
    google_provider_cfg = requests.get(GOOGLE_DISCOVERY_URL).json()
    token_endpoint = google_provider_cfg["token_endpoint"]

    # Prepare token request
    token_url, headers, body = client.prepare_token_request(
        token_endpoint,
        authorization_response=request.url.replace("http://", "https://"),
        redirect_url=request.base_url.replace("http://", "https://"),
        code=code,
    )
    
    # Exchange code for token
    token_response = requests.post(
        token_url,
        headers=headers,
        data=body,
        auth=(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET),
    )

    # Parse the tokens
    client.parse_request_body_response(json.dumps(token_response.json()))

    # Get user info from Google
    userinfo_endpoint = google_provider_cfg["userinfo_endpoint"]
    uri, headers, body = client.add_token(userinfo_endpoint)
    userinfo_response = requests.get(uri, headers=headers, data=body)

    # Process user information
    userinfo = userinfo_response.json()
    if userinfo.get("email_verified"):
        email = userinfo["email"]
        name = userinfo.get("name", userinfo.get("given_name", "User"))
        
        # Check if user exists
        user_id, user = data_store.get_user_by_email(email)
        
        if not user:
            # Create new user
            user_id, user = data_store.create_user(
                email=email,
                password=None,  # OAuth users don't need password
                name=name,
                date_of_birth=None  # Will be collected later
            )
            flash(f'Welcome to HeartGrid, {name}! Please complete your profile.', 'success')
        else:
            flash(f'Welcome back, {name}!', 'success')
        
        # Log user in
        session['user_id'] = user_id
        session['user_name'] = name
        session['auth_method'] = 'google'
        
        return redirect(url_for('profile'))
    else:
        flash('Google authentication failed. Email not verified.', 'error')
        return redirect(url_for('login'))

@google_auth.route("/facebook_login")
def facebook_login():
    # Placeholder for Facebook OAuth - similar implementation
    flash('Facebook login will be available soon!', 'info')
    return redirect(url_for('login'))