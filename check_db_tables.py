#!/usr/bin/env python
"""
Check actual database table names
"""

import sqlite3

def check_tables():
    try:
        conn = sqlite3.connect('db.sqlite3')
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        print("📊 All Database Tables:")
        for table in sorted(tables):
            print(f"   - {table}")
        
        # Look for heartgrid related tables
        heartgrid_tables = [t for t in tables if 'heartgrid' in t.lower()]
        print(f"\n💖 HeartGrid Tables ({len(heartgrid_tables)}):")
        for table in heartgrid_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   - {table}: {count} records")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    check_tables()
