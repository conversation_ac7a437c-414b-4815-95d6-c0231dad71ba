"""
AI-Powered Matching Algorithm for HeartGrid
Advanced compatibility scoring and intelligent profile recommendations
"""

import math
import random
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class AIMatchingEngine:
    """Advanced AI-powered matching algorithm with compatibility scoring"""
    
    def __init__(self, data_store):
        self.data_store = data_store
        
        # Compatibility weights for different factors
        self.weights = {
            'interests': 0.25,
            'age_compatibility': 0.20,
            'location_proximity': 0.15,
            'activity_pattern': 0.15,
            'communication_style': 0.10,
            'profile_completeness': 0.10,
            'mutual_preferences': 0.05
        }
        
        # Interest categories for better matching
        self.interest_categories = {
            'outdoors': ['Hiking', 'Nature', 'Travel', 'Sports'],
            'creative': ['Art', 'Music', 'Photography', 'Dancing'],
            'intellectual': ['Reading', 'Technology', 'Movies'],
            'lifestyle': ['Fitness', 'Cooking', 'Food', 'Fashion', 'Yoga'],
            'social': ['Gaming', 'Animals']
        }
        
    def calculate_compatibility_score(self, user1_id: str, user2_id: str) -> float:
        """Calculate comprehensive compatibility score between two users"""
        
        profile1 = self.data_store.get_profile(user1_id)
        profile2 = self.data_store.get_profile(user2_id)
        user1 = self.data_store.get_user_by_id(user1_id)
        user2 = self.data_store.get_user_by_id(user2_id)
        
        if not all([profile1, profile2, user1, user2]):
            return 0.0
        
        scores = {}
        
        # 1. Interest compatibility
        scores['interests'] = self._calculate_interest_compatibility(profile1, profile2)
        
        # 2. Age compatibility
        scores['age_compatibility'] = self._calculate_age_compatibility(profile1, profile2)
        
        # 3. Location proximity (mock implementation)
        scores['location_proximity'] = self._calculate_location_compatibility(profile1, profile2)
        
        # 4. Activity pattern matching
        scores['activity_pattern'] = self._calculate_activity_compatibility(user1_id, user2_id)
        
        # 5. Communication style compatibility
        scores['communication_style'] = self._calculate_communication_compatibility(user1_id, user2_id)
        
        # 6. Profile completeness factor
        scores['profile_completeness'] = self._calculate_profile_completeness_factor(profile1, profile2)
        
        # 7. Mutual preference alignment
        scores['mutual_preferences'] = self._calculate_mutual_preferences(profile1, profile2)
        
        # Calculate weighted final score
        final_score = sum(scores[factor] * self.weights[factor] for factor in scores)
        
        return min(max(final_score, 0.0), 1.0)  # Normalize to 0-1
    
    def _calculate_interest_compatibility(self, profile1: Dict, profile2: Dict) -> float:
        """Calculate interest-based compatibility"""
        interests1 = set(profile1.get('interests', []))
        interests2 = set(profile2.get('interests', []))
        
        if not interests1 or not interests2:
            return 0.3  # Neutral score for missing data
        
        # Direct interest overlap
        common_interests = interests1.intersection(interests2)
        direct_score = len(common_interests) / max(len(interests1), len(interests2))
        
        # Category-based compatibility
        categories1 = self._get_interest_categories(interests1)
        categories2 = self._get_interest_categories(interests2)
        common_categories = categories1.intersection(categories2)
        category_score = len(common_categories) / max(len(categories1), len(categories2), 1)
        
        return (direct_score * 0.7) + (category_score * 0.3)
    
    def _get_interest_categories(self, interests: set) -> set:
        """Map interests to categories"""
        categories = set()
        for interest in interests:
            for category, category_interests in self.interest_categories.items():
                if interest in category_interests:
                    categories.add(category)
        return categories
    
    def _calculate_age_compatibility(self, profile1: Dict, profile2: Dict) -> float:
        """Calculate age-based compatibility"""
        age1 = profile1.get('age')
        age2 = profile2.get('age')
        
        if not age1 or not age2:
            return 0.5  # Neutral score for missing age
        
        age_diff = abs(age1 - age2)
        
        # Optimal age difference scoring
        if age_diff <= 2:
            return 1.0
        elif age_diff <= 5:
            return 0.8
        elif age_diff <= 8:
            return 0.6
        elif age_diff <= 12:
            return 0.4
        else:
            return 0.2
    
    def _calculate_location_compatibility(self, profile1: Dict, profile2: Dict) -> float:
        """Calculate location-based compatibility (simplified)"""
        location1 = profile1.get('location', '').lower()
        location2 = profile2.get('location', '').lower()
        
        if not location1 or not location2:
            return 0.5
        
        # Simple location matching - in real implementation, use geocoding
        if location1 == location2:
            return 1.0
        
        # Check for same city/state components
        loc1_parts = set(location1.split(','))
        loc2_parts = set(location2.split(','))
        common_parts = loc1_parts.intersection(loc2_parts)
        
        if common_parts:
            return 0.7
        
        return 0.3  # Different locations
    
    def _calculate_activity_compatibility(self, user1_id: str, user2_id: str) -> float:
        """Calculate activity pattern compatibility"""
        # Mock implementation - in real app, analyze user activity patterns
        # This would look at login times, messaging patterns, etc.
        
        # Simulate activity pattern analysis
        patterns = ['morning', 'afternoon', 'evening', 'night']
        user1_pattern = hash(user1_id) % len(patterns)
        user2_pattern = hash(user2_id) % len(patterns)
        
        if user1_pattern == user2_pattern:
            return 1.0
        elif abs(user1_pattern - user2_pattern) == 1:
            return 0.7
        else:
            return 0.4
    
    def _calculate_communication_compatibility(self, user1_id: str, user2_id: str) -> float:
        """Calculate communication style compatibility"""
        # Mock implementation - analyze message length, response time, emoji usage
        
        # Get user messages for analysis
        messages1 = []
        messages2 = []
        
        for convo in self.data_store.conversations.values():
            for msg in convo:
                if msg['sender_id'] == user1_id:
                    messages1.append(msg)
                elif msg['sender_id'] == user2_id:
                    messages2.append(msg)
        
        if not messages1 or not messages2:
            return 0.6  # Neutral for new users
        
        # Analyze message characteristics
        avg_length1 = sum(len(msg['message']) for msg in messages1[-10:]) / min(len(messages1), 10)
        avg_length2 = sum(len(msg['message']) for msg in messages2[-10:]) / min(len(messages2), 10)
        
        # Similar message lengths indicate compatible communication styles
        length_diff = abs(avg_length1 - avg_length2)
        if length_diff < 20:
            return 0.9
        elif length_diff < 50:
            return 0.7
        else:
            return 0.5
    
    def _calculate_profile_completeness_factor(self, profile1: Dict, profile2: Dict) -> float:
        """Factor in profile completeness for better matching"""
        def completeness_score(profile):
            fields = ['bio', 'interests', 'photos', 'age', 'location']
            completed = sum(1 for field in fields if profile.get(field))
            return completed / len(fields)
        
        comp1 = completeness_score(profile1)
        comp2 = completeness_score(profile2)
        
        # Reward high mutual completeness
        return (comp1 + comp2) / 2
    
    def _calculate_mutual_preferences(self, profile1: Dict, profile2: Dict) -> float:
        """Calculate mutual preference compatibility"""
        pref1 = profile1.get('interested_in', 'everyone')
        pref2 = profile2.get('interested_in', 'everyone')
        gender1 = profile1.get('gender', '')
        gender2 = profile2.get('gender', '')
        
        # Check if users meet each other's preferences
        compatible1 = pref1 == 'everyone' or pref1 == gender2
        compatible2 = pref2 == 'everyone' or pref2 == gender1
        
        if compatible1 and compatible2:
            return 1.0
        elif compatible1 or compatible2:
            return 0.5
        else:
            return 0.0
    
    def get_smart_recommendations(self, user_id: str, limit: int = 20) -> List[Dict]:
        """Get AI-powered profile recommendations with compatibility scores"""
        
        user_profile = self.data_store.get_profile(user_id)
        if not user_profile:
            return []
        
        # Get all discoverable profiles
        all_profiles = self.data_store.get_discoverable_profiles(user_id, limit * 3)
        
        # Calculate compatibility scores
        scored_profiles = []
        for profile in all_profiles:
            compatibility = self.calculate_compatibility_score(user_id, profile['user_id'])
            
            profile_data = profile.copy()
            profile_data['compatibility_score'] = compatibility
            profile_data['match_reasons'] = self._generate_match_reasons(user_id, profile['user_id'])
            
            scored_profiles.append(profile_data)
        
        # Sort by compatibility score and apply diversity
        scored_profiles.sort(key=lambda x: x['compatibility_score'], reverse=True)
        
        # Apply diversity algorithm to avoid showing only similar profiles
        diverse_profiles = self._apply_diversity_filter(scored_profiles, limit)
        
        return diverse_profiles
    
    def _generate_match_reasons(self, user1_id: str, user2_id: str) -> List[str]:
        """Generate human-readable reasons for the match"""
        
        profile1 = self.data_store.get_profile(user1_id)
        profile2 = self.data_store.get_profile(user2_id)
        reasons = []
        
        # Common interests
        interests1 = set(profile1.get('interests', []))
        interests2 = set(profile2.get('interests', []))
        common = interests1.intersection(interests2)
        
        if common:
            if len(common) == 1:
                reasons.append(f"You both love {list(common)[0]}")
            else:
                reasons.append(f"You share {len(common)} interests")
        
        # Age compatibility
        age1 = profile1.get('age')
        age2 = profile2.get('age')
        if age1 and age2 and abs(age1 - age2) <= 3:
            reasons.append("Similar age")
        
        # Location
        loc1 = profile1.get('location', '').lower()
        loc2 = profile2.get('location', '').lower()
        if loc1 and loc2 and loc1 == loc2:
            reasons.append("Same location")
        
        # Profile quality
        if len(profile2.get('photos', [])) >= 3:
            reasons.append("Complete profile")
        
        return reasons[:3]  # Limit to top 3 reasons
    
    def _apply_diversity_filter(self, profiles: List[Dict], limit: int) -> List[Dict]:
        """Apply diversity to avoid showing too similar profiles"""
        
        if len(profiles) <= limit:
            return profiles
        
        selected = []
        remaining = profiles.copy()
        
        # Always include top compatibility match
        if remaining:
            selected.append(remaining.pop(0))
        
        # Add diverse profiles
        while len(selected) < limit and remaining:
            best_diversity_score = -1
            best_profile = None
            best_index = -1
            
            for i, profile in enumerate(remaining):
                diversity_score = self._calculate_diversity_score(profile, selected)
                combined_score = profile['compatibility_score'] * 0.7 + diversity_score * 0.3
                
                if combined_score > best_diversity_score:
                    best_diversity_score = combined_score
                    best_profile = profile
                    best_index = i
            
            if best_profile:
                selected.append(remaining.pop(best_index))
            else:
                break
        
        return selected
    
    def _calculate_diversity_score(self, candidate: Dict, selected: List[Dict]) -> float:
        """Calculate how diverse a candidate is from already selected profiles"""
        
        if not selected:
            return 1.0
        
        diversity_scores = []
        
        for selected_profile in selected:
            # Compare interests
            interests1 = set(candidate.get('interests', []))
            interests2 = set(selected_profile.get('interests', []))
            interest_diversity = 1.0 - (len(interests1.intersection(interests2)) / max(len(interests1.union(interests2)), 1))
            
            # Compare age
            age1 = candidate.get('age', 25)
            age2 = selected_profile.get('age', 25)
            age_diversity = min(abs(age1 - age2) / 10.0, 1.0)
            
            # Combined diversity for this pair
            pair_diversity = (interest_diversity + age_diversity) / 2
            diversity_scores.append(pair_diversity)
        
        # Return minimum diversity (most conservative)
        return min(diversity_scores)

# Global AI matching engine instance
ai_matching_engine = None

def init_ai_matching(data_store):
    global ai_matching_engine
    ai_matching_engine = AIMatchingEngine(data_store)
    return ai_matching_engine