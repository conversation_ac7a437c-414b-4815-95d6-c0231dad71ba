#!/usr/bin/env python
"""
Simple test to check basic functionality without Django setup
"""

import os
import sys

def check_files():
    """Check if required files exist"""
    print("📁 Checking Files...")
    
    required_files = [
        'manage.py',
        'heartgrid_django/settings.py',
        'heartgrid/models.py',
        'heartgrid/views.py',
        'heartgrid/urls.py',
        'heartgrid/frontend_urls.py',
        'templates/index.html',
        'templates/login.html',
        'templates/register.html',
        'templates/discover_modern.html',
        'templates/profile.html',
        'templates/matches.html',
        'templates/chat.html',
        'templates/subscription.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_syntax():
    """Check Python syntax of key files"""
    print("\n🐍 Checking Python Syntax...")
    
    python_files = [
        'heartgrid/models.py',
        'heartgrid/views.py',
        'heartgrid/urls.py',
        'heartgrid/frontend_urls.py',
        'heartgrid_django/settings.py',
        'heartgrid_django/urls.py'
    ]
    
    syntax_errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            print(f"   ✅ {file_path}")
        except SyntaxError as e:
            print(f"   ❌ {file_path}: {e}")
            syntax_errors.append(file_path)
        except Exception as e:
            print(f"   ⚠️  {file_path}: {e}")
    
    return len(syntax_errors) == 0

def check_database():
    """Check if database file exists"""
    print("\n🗄️  Checking Database...")
    
    if os.path.exists('db.sqlite3'):
        size = os.path.getsize('db.sqlite3')
        print(f"   ✅ Database exists ({size} bytes)")
        return True
    else:
        print("   ❌ Database file not found")
        return False

def check_virtual_env():
    """Check if virtual environment is set up"""
    print("\n🐍 Checking Virtual Environment...")
    
    if os.path.exists('venv'):
        print("   ✅ Virtual environment directory exists")
        
        # Check for Django installation
        django_path = 'venv/Lib/site-packages/django'
        if os.path.exists(django_path):
            print("   ✅ Django installed in virtual environment")
            return True
        else:
            print("   ❌ Django not found in virtual environment")
            return False
    else:
        print("   ❌ Virtual environment not found")
        return False

def main():
    print("🔍 HeartGrid Simple Test")
    print("=" * 40)
    
    files_ok = check_files()
    syntax_ok = check_syntax()
    db_ok = check_database()
    venv_ok = check_virtual_env()
    
    print(f"\n📊 Summary:")
    print(f"   Files: {'✅' if files_ok else '❌'}")
    print(f"   Syntax: {'✅' if syntax_ok else '❌'}")
    print(f"   Database: {'✅' if db_ok else '❌'}")
    print(f"   Virtual Env: {'✅' if venv_ok else '❌'}")
    
    overall_ok = all([files_ok, syntax_ok, db_ok, venv_ok])
    
    print(f"\n🎯 Overall: {'✅ READY' if overall_ok else '❌ ISSUES DETECTED'}")
    
    if overall_ok:
        print("\n💡 Next steps:")
        print("   1. Run: venv\\Scripts\\python.exe manage.py runserver")
        print("   2. Open: http://127.0.0.1:8000")
        print("   3. Test registration and login")
    else:
        print("\n⚠️  Fix the issues above before proceeding")

if __name__ == '__main__':
    main()
