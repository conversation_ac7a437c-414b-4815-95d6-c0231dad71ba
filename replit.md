# HeartGrid - Dating Application

## Project Overview
HeartGrid is a modern web-based dating application built with Flask and vanilla JavaScript, featuring:
- Grid-style profile discovery with like/dislike functionality
- Google OAuth authentication integration
- Cryptocurrency payment system supporting USDT and native tokens on multiple blockchains
- 3-day trial period followed by subscription-based access
- Voice/video calling for premium subscribers
- Admin dashboard with user management and analytics

## Recent Changes (December 2024)
- ✅ Implemented AI-powered matching algorithm with compatibility scoring and smart recommendations
- ✅ Added comprehensive gamification system with achievements, streaks, daily challenges, and rewards
- ✅ Built push notification system for real-time engagement (matches, messages, achievements)
- ✅ Enhanced discovery with clickable profiles and detailed profile modals with chat access
- ✅ Created premium features system with cryptocurrency payments (ETH, BNB, SOL, TRX, TON)
- ✅ Integrated activity tracking, daily limits, and subscription management
- ✅ Added user analytics, profile insights, and AI-powered recommendations

## Technical Architecture

### Backend (Flask)
- **models.py**: In-memory data store with user management, profiles, matches, subscriptions
- **crypto_payment.py**: Cryptocurrency payment processor supporting 5 major blockchains
- **routes.py**: All application endpoints including crypto payment routes
- **google_auth.py**: OAuth authentication handling

### Frontend
- **Bootstrap 5**: Modern responsive UI framework
- **Vanilla JavaScript**: Interactive features and AJAX requests
- **Custom CSS**: Professional dating app styling
- **QR Code Integration**: For cryptocurrency payments

### Payment System
- **Supported Networks**: Ethereum, BNB Smart Chain, Solana, Tron, TON
- **Supported Tokens**: Native tokens (ETH, BNB, SOL, TRX, TON) and USDT
- **Real-time Pricing**: CoinGecko API integration for current crypto prices
- **Payment Tracking**: Unique payment addresses and transaction monitoring

## Subscription Plans
- **Weekly**: $5.00 (7 days access)
- **Fortnightly**: $9.99 (14 days access) - Most Popular
- **Monthly**: $15.99 (30 days access) - Best Value

## Admin Access
- Email: <EMAIL>
- Password: Admin123!
- Features: User management, analytics dashboard, subscription oversight

## Future Development Roadmap
See feature_suggestions.md for comprehensive enhancement recommendations including:
- Mobile app development with native iOS/Android apps
- AI-powered matching algorithm and compatibility scoring
- Enhanced social features (events, groups, stories)
- Advanced security (photo verification, background checks)
- Rich messaging (voice notes, media sharing, reactions)
- Gamification elements (achievements, streaks, rewards)
- Business partnerships for date planning and local integration

## User Preferences
*No specific preferences recorded yet*

## Deployment Notes
- Application runs on port 5000 with Gunicorn
- Uses in-memory data store (suitable for demo/development)
- Ready for production deployment with database migration
- Environment variables configured for OAuth and database connections