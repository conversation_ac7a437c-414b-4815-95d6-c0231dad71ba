# HeartGrid - Dating Application

## Project Overview
HeartGrid is a modern web-based dating application built with Django and DaisyUI, featuring:
- Grid-style profile discovery with like/dislike functionality
- Google OAuth authentication integration
- Cryptocurrency payment system supporting USDT and native tokens on multiple blockchains
- 3-day trial period followed by subscription-based access
- Voice/video calling for premium subscribers
- Admin dashboard with user management and analytics

## Recent Changes (December 2024)
- ✅ **MAJOR MIGRATION**: Successfully migrated from Flask to Django with Django REST Framework
- ✅ Implemented comprehensive Django models with SQLite database
- ✅ Created Django REST API endpoints for all functionality
- ✅ Integrated Django authentication system with CSRF protection
- ✅ Converted frontend to modern DaisyUI/Tailwind CSS design
- ✅ Added JavaScript API integration layer for seamless frontend-backend communication
- ✅ Preserved all features: AI matching, gamification, crypto payments, premium features
- ✅ Enhanced security with Django's built-in protection mechanisms

## Technical Architecture

### Backend (Django)
- **Django 5.2.3**: Modern Python web framework with built-in security
- **Django REST Framework**: Comprehensive API framework for frontend integration
- **SQLite Database**: Persistent data storage with Django ORM
- **Custom User Model**: Extended authentication with email-based login
- **Django Models**: Complete data models for users, profiles, matches, subscriptions, payments
- **CSRF Protection**: Built-in security for all forms and API requests

### Frontend
- **DaisyUI + Tailwind CSS**: Modern component library with utility-first CSS
- **JavaScript API Layer**: Comprehensive API integration with error handling
- **Django Templates**: Server-side rendering with Django template engine
- **Static File Serving**: Optimized CSS/JS delivery through Django

### Payment System
- **Supported Networks**: Ethereum, BNB Smart Chain, Solana, Tron, TON
- **Supported Tokens**: Native tokens (ETH, BNB, SOL, TRX, TON) and USDT
- **Real-time Pricing**: CoinGecko API integration for current crypto prices
- **Payment Tracking**: Unique payment addresses and transaction monitoring

## Subscription Plans
- **Weekly**: $5.00 (7 days access)
- **Fortnightly**: $9.99 (14 days access) - Most Popular
- **Monthly**: $15.99 (30 days access) - Best Value

## Admin Access
- Email: <EMAIL>
- Password: Admin123!
- Features: User management, analytics dashboard, subscription oversight

## Future Development Roadmap
See feature_suggestions.md for comprehensive enhancement recommendations including:
- Mobile app development with native iOS/Android apps
- AI-powered matching algorithm and compatibility scoring
- Enhanced social features (events, groups, stories)
- Advanced security (photo verification, background checks)
- Rich messaging (voice notes, media sharing, reactions)
- Gamification elements (achievements, streaks, rewards)
- Business partnerships for date planning and local integration

## User Preferences
*No specific preferences recorded yet*

## Deployment Notes
- Application runs on port 8000 with Django development server
- Uses SQLite database with Django ORM (production-ready)
- Django migrations handle database schema management
- Environment variables configured for Django settings and API keys
- Ready for production deployment with PostgreSQL/MySQL migration