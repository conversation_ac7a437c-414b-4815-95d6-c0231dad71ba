#!/usr/bin/env python3
"""
NOWPayments Integration Test Script

Tests the NOWPayments button integration in HeartGrid Django application
"""

import requests
import time
from bs4 import BeautifulSoup

BASE_URL = 'http://localhost:8000'

def test_subscription_page():
    """Test that the subscription page loads and contains NOWPayments button"""
    print("🧪 Testing subscription page...")
    
    try:
        response = requests.get(f'{BASE_URL}/subscription/')
        
        if response.status_code == 200:
            print("✅ Subscription page loads successfully")
            
            # Parse HTML to check for NOWPayments button
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for NOWPayments button
            nowpayments_btn = soup.find('a', {'href': 'https://nowpayments.io/payment/?iid=5532080560&source=button'})
            if nowpayments_btn:
                print("✅ NOWPayments button found in subscription page")
                print(f"   Button text: {nowpayments_btn.get_text().strip()}")
                
                # Check for proper attributes
                if nowpayments_btn.get('target') == '_blank':
                    print("✅ Button opens in new tab (target='_blank')")
                else:
                    print("❌ Button missing target='_blank' attribute")
                    
                if nowpayments_btn.get('rel') == 'noreferrer noopener':
                    print("✅ Button has proper security attributes")
                else:
                    print("❌ Button missing security attributes")
                    
                # Check for NOWPayments image
                img = nowpayments_btn.find('img')
                if img and 'nowpayments.io' in img.get('src', ''):
                    print("✅ NOWPayments image found")
                else:
                    print("❌ NOWPayments image missing or incorrect")
                    
            else:
                print("❌ NOWPayments button not found in subscription page")
                
            # Check for weekly plan section
            weekly_section = soup.find('div', class_='col-md-4')
            if weekly_section and 'Weekly' in weekly_section.get_text():
                print("✅ Weekly plan section found")
            else:
                print("❌ Weekly plan section not found")
                
        else:
            print(f"❌ Subscription page failed to load: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server. Make sure it's running on localhost:8000")
    except Exception as e:
        print(f"❌ Error testing subscription page: {e}")

def test_payment_options_page():
    """Test payment options page for NOWPayments integration"""
    print("\n🧪 Testing payment options page...")
    
    try:
        # Test with weekly plan parameter
        response = requests.get(f'{BASE_URL}/payment-options/?plan=weekly')
        
        if response.status_code == 200:
            print("✅ Payment options page loads successfully")
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for NOWPayments button (should only appear for weekly plan)
            nowpayments_btn = soup.find('a', {'href': 'https://nowpayments.io/payment/?iid=5532080560&source=button'})
            if nowpayments_btn:
                print("✅ NOWPayments button found in payment options (weekly plan)")
            else:
                print("ℹ️  NOWPayments button not found in payment options (expected for non-weekly plans)")
                
        else:
            print(f"❌ Payment options page failed to load: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server")
    except Exception as e:
        print(f"❌ Error testing payment options page: {e}")

def test_nowpayments_url():
    """Test that the NOWPayments URL is accessible"""
    print("\n🧪 Testing NOWPayments URL accessibility...")
    
    try:
        # Test the NOWPayments URL (with a short timeout)
        response = requests.get(
            'https://nowpayments.io/payment/?iid=5532080560&source=button',
            timeout=10,
            allow_redirects=True
        )
        
        if response.status_code == 200:
            print("✅ NOWPayments URL is accessible")
        else:
            print(f"⚠️  NOWPayments URL returned status: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("⚠️  NOWPayments URL timeout (this is normal)")
    except requests.exceptions.ConnectionError:
        print("⚠️  Cannot connect to NOWPayments (check internet connection)")
    except Exception as e:
        print(f"⚠️  Error testing NOWPayments URL: {e}")

def test_responsive_design():
    """Test responsive design elements"""
    print("\n🧪 Testing responsive design...")
    
    try:
        response = requests.get(f'{BASE_URL}/subscription/')
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check for responsive CSS classes
            responsive_elements = soup.find_all(class_=lambda x: x and ('col-md' in x or 'w-100' in x))
            if responsive_elements:
                print("✅ Responsive CSS classes found")
            else:
                print("❌ No responsive CSS classes found")
                
            # Check for mobile-friendly meta tag
            viewport_meta = soup.find('meta', {'name': 'viewport'})
            if viewport_meta:
                print("✅ Viewport meta tag found (mobile-friendly)")
            else:
                print("❌ Viewport meta tag missing")
                
        else:
            print("❌ Cannot test responsive design - page not loading")
            
    except Exception as e:
        print(f"❌ Error testing responsive design: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting NOWPayments Integration Tests")
    print("=" * 50)
    
    test_subscription_page()
    test_payment_options_page()
    test_nowpayments_url()
    test_responsive_design()
    
    print("\n" + "=" * 50)
    print("✅ NOWPayments integration testing completed!")
    print("\nNext steps:")
    print("1. Manually test the button click in browser")
    print("2. Verify the button opens NOWPayments in new tab")
    print("3. Test on different screen sizes")
    print("4. Verify styling matches the rest of the application")

if __name__ == "__main__":
    main()
