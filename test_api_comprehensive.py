#!/usr/bin/env python3
"""
HeartGrid Comprehensive API Test Suite

This script tests all the Django REST API endpoints to ensure they work correctly.
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = 'http://localhost:8000/api/v1'
TEST_USER_DATA = {
    'email': f'test_{int(time.time())}@heartgrid.com',  # Unique email
    'name': 'Test User',
    'password': 'testpassword123',
    'age': 25,
    'location': 'Cape Town, South Africa'
}

class HeartGridAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.user_id = None
        self.test_results = []
        
    def log_result(self, test_name, success, message=""):
        """Log test result"""
        status = "✓" if success else "✗"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append((test_name, success, message))
        
    def test_server_connection(self):
        """Test if Django server is running"""
        print("Testing server connection...")
        try:
            response = self.session.get(f'{BASE_URL}/')
            if response.status_code in [200, 404]:  # 404 is OK, means server is running
                self.log_result("Server Connection", True, "Server is running")
                return True
            else:
                self.log_result("Server Connection", False, f"Unexpected status: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_result("Server Connection", False, "Cannot connect to server")
            return False
        
    def test_user_registration(self):
        """Test user registration endpoint"""
        print("Testing user registration...")
        
        try:
            response = self.session.post(f'{BASE_URL}/register/', json=TEST_USER_DATA)
            
            if response.status_code == 201:
                data = response.json()
                self.auth_token = data.get('token')
                self.user_id = data.get('user', {}).get('id')
                
                if self.auth_token:
                    # Set authorization header for future requests
                    self.session.headers.update({'Authorization': f'Token {self.auth_token}'})
                    self.log_result("User Registration", True, f"Token received: {self.auth_token[:20]}...")
                    return True
                else:
                    self.log_result("User Registration", False, "No token in response")
                    return False
            else:
                self.log_result("User Registration", False, f"Status {response.status_code}: {response.text[:100]}")
                return False
        except Exception as e:
            self.log_result("User Registration", False, f"Exception: {str(e)}")
            return False
    
    def test_user_login(self):
        """Test user login endpoint"""
        print("Testing user login...")
        
        login_data = {
            'email': TEST_USER_DATA['email'],
            'password': TEST_USER_DATA['password']
        }
        
        try:
            response = self.session.post(f'{BASE_URL}/login/', json=login_data)
            
            if response.status_code == 200:
                self.log_result("User Login", True)
                return True
            else:
                self.log_result("User Login", False, f"Status {response.status_code}: {response.text[:100]}")
                return False
        except Exception as e:
            self.log_result("User Login", False, f"Exception: {str(e)}")
            return False
    
    def test_profile_endpoints(self):
        """Test profile-related endpoints"""
        print("Testing profile endpoints...")
        
        # Get profile
        try:
            response = self.session.get(f'{BASE_URL}/profile/')
            if response.status_code == 200:
                self.log_result("Get Profile", True)
            else:
                self.log_result("Get Profile", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Profile", False, f"Exception: {str(e)}")
        
        # Update profile
        profile_data = {
            'bio': 'Updated bio for testing',
            'interests': ['music', 'travel', 'fitness']
        }
        
        try:
            response = self.session.put(f'{BASE_URL}/profile/', json=profile_data)
            if response.status_code == 200:
                self.log_result("Update Profile", True)
            else:
                self.log_result("Update Profile", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Update Profile", False, f"Exception: {str(e)}")
    
    def test_discovery_endpoints(self):
        """Test discovery and matching endpoints"""
        print("Testing discovery endpoints...")
        
        try:
            response = self.session.get(f'{BASE_URL}/discover/')
            if response.status_code == 200:
                self.log_result("Discovery Endpoint", True)
            else:
                self.log_result("Discovery Endpoint", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Discovery Endpoint", False, f"Exception: {str(e)}")
    
    def test_subscription_endpoints(self):
        """Test subscription and payment endpoints"""
        print("Testing subscription endpoints...")
        
        # Get subscription info
        try:
            response = self.session.get(f'{BASE_URL}/subscription/')
            if response.status_code == 200:
                self.log_result("Get Subscription", True)
            else:
                self.log_result("Get Subscription", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Subscription", False, f"Exception: {str(e)}")
        
        # Test supported chains
        try:
            response = self.session.get(f'{BASE_URL}/payment/chains/')
            if response.status_code == 200:
                self.log_result("Get Supported Chains", True)
            else:
                self.log_result("Get Supported Chains", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Supported Chains", False, f"Exception: {str(e)}")
        
        # Test crypto payment creation
        payment_data = {
            'plan': 'weekly',
            'chain': 'ethereum',
            'token_type': 'native'
        }
        
        try:
            response = self.session.post(f'{BASE_URL}/payment/crypto/', json=payment_data)
            if response.status_code == 200:
                self.log_result("Create Crypto Payment", True)
            else:
                self.log_result("Create Crypto Payment", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Create Crypto Payment", False, f"Exception: {str(e)}")
    
    def test_gamification_endpoints(self):
        """Test gamification and achievement endpoints"""
        print("Testing gamification endpoints...")
        
        # Get user stats
        try:
            response = self.session.get(f'{BASE_URL}/stats/')
            if response.status_code == 200:
                self.log_result("Get User Stats", True)
            else:
                self.log_result("Get User Stats", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get User Stats", False, f"Exception: {str(e)}")
        
        # Get achievements
        try:
            response = self.session.get(f'{BASE_URL}/achievements/')
            if response.status_code == 200:
                self.log_result("Get Achievements", True)
            else:
                self.log_result("Get Achievements", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Achievements", False, f"Exception: {str(e)}")
        
        # Get leaderboard
        try:
            response = self.session.get(f'{BASE_URL}/leaderboard/')
            if response.status_code == 200:
                self.log_result("Get Leaderboard", True)
            else:
                self.log_result("Get Leaderboard", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Leaderboard", False, f"Exception: {str(e)}")
        
        # Trigger achievement check
        try:
            response = self.session.post(f'{BASE_URL}/achievements/check/')
            if response.status_code == 200:
                self.log_result("Trigger Achievement Check", True)
            else:
                self.log_result("Trigger Achievement Check", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Trigger Achievement Check", False, f"Exception: {str(e)}")
    
    def test_premium_features(self):
        """Test premium feature endpoints"""
        print("Testing premium features...")
        
        # Get premium features
        try:
            response = self.session.get(f'{BASE_URL}/premium/features/')
            if response.status_code == 200:
                self.log_result("Get Premium Features", True)
            else:
                self.log_result("Get Premium Features", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Premium Features", False, f"Exception: {str(e)}")
        
        # Check premium access
        try:
            response = self.session.get(f'{BASE_URL}/premium/check/super_likes/')
            if response.status_code == 200:
                self.log_result("Check Premium Access", True)
            else:
                self.log_result("Check Premium Access", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Check Premium Access", False, f"Exception: {str(e)}")
    
    def test_notifications_endpoints(self):
        """Test notification endpoints"""
        print("Testing notification endpoints...")
        
        try:
            response = self.session.get(f'{BASE_URL}/notifications/')
            if response.status_code == 200:
                self.log_result("Get Notifications", True)
            else:
                self.log_result("Get Notifications", False, f"Status {response.status_code}")
        except Exception as e:
            self.log_result("Get Notifications", False, f"Exception: {str(e)}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total*100):.1f}%")
        
        if total - passed > 0:
            print("\nFailed Tests:")
            for test_name, success, message in self.test_results:
                if not success:
                    print(f"  ✗ {test_name}: {message}")
        
        print("=" * 60)
    
    def run_all_tests(self):
        """Run all API tests"""
        print("=" * 60)
        print("HeartGrid Django API Test Suite")
        print("=" * 60)
        
        # Test server connection first
        if not self.test_server_connection():
            print("Server connection failed, stopping tests")
            return False
        
        # Test registration first
        if not self.test_user_registration():
            print("Registration failed, continuing with other tests...")
        
        # Run all other tests
        self.test_user_login()
        self.test_profile_endpoints()
        self.test_discovery_endpoints()
        self.test_subscription_endpoints()
        self.test_gamification_endpoints()
        self.test_premium_features()
        self.test_notifications_endpoints()
        
        self.print_summary()
        
        return True

if __name__ == '__main__':
    tester = HeartGridAPITester()
    success = tester.run_all_tests()
    
    if not success:
        sys.exit(1)
