"""
HeartGrid URL Configuration

This module contains all the URL patterns for the HeartGrid dating platform API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for ViewSets
router = DefaultRouter()
router.register(r'profiles', views.ProfileViewSet, basename='profile')

app_name = 'heartgrid'

urlpatterns = [
    # Authentication URLs
    path('auth/register/', views.UserRegistrationView.as_view(), name='register'),
    path('auth/login/', views.user_login, name='login'),
    path('auth/logout/', views.user_logout, name='logout'),
    
    # Profile URLs
    path('profile/upload-photo/', views.upload_photo, name='upload_photo'),
    path('profile/delete-photo/<uuid:photo_id>/', views.delete_photo, name='delete_photo'),
    
    # Discovery and Matching URLs
    path('discover/', views.discover_profiles, name='discover'),
    path('like/', views.like_profile, name='like_profile'),
    path('matches/', views.get_matches, name='matches'),
    
    # Messaging URLs
    path('chat/<uuid:match_user_id>/', views.get_conversation, name='conversation'),
    path('send-message/', views.send_message, name='send_message'),
    path('message/<uuid:message_id>/read/', views.mark_message_read, name='mark_message_read'),

    # Subscription and Payment URLs
    path('subscription/', views.get_subscription, name='subscription'),
    path('payment/crypto/', views.create_crypto_payment, name='crypto_payment'),
    path('payment/verify/', views.verify_crypto_payment, name='verify_crypto_payment'),
    path('payment/<uuid:payment_id>/status/', views.get_payment_status, name='payment_status'),
    path('payment/chains/', views.get_supported_chains, name='supported_chains'),

    # Notification URLs
    path('notifications/', views.get_notifications, name='notifications'),
    path('notification/<uuid:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),

    # User Stats and Achievements
    path('stats/', views.get_user_stats, name='user_stats'),

    # Premium Features URLs
    path('premium/check/<str:feature>/', views.check_premium_access, name='check_premium_access'),
    path('premium/super-like/', views.use_super_like, name='use_super_like'),
    path('premium/voice-message/', views.send_voice_message, name='send_voice_message'),
    path('premium/features/', views.get_premium_features, name='premium_features'),

    # Gamification URLs
    path('achievements/', views.get_achievements, name='achievements'),
    path('leaderboard/', views.get_leaderboard, name='leaderboard'),
    path('achievements/check/', views.trigger_achievement_check, name='trigger_achievement_check'),

    # Admin URLs
    path('admin/analytics/', views.get_admin_analytics, name='admin_analytics'),

    # Include router URLs
    path('', include(router.urls)),
]
