"""
HeartGrid URL Configuration

This module contains all the URL patterns for the HeartGrid dating platform API.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for ViewSets
router = DefaultRouter()
router.register(r'profiles', views.ProfileViewSet, basename='profile')

app_name = 'heartgrid'

urlpatterns = [
    # Authentication URLs
    path('auth/register/', views.UserRegistrationView.as_view(), name='register'),
    path('auth/login/', views.user_login, name='login'),
    path('auth/logout/', views.user_logout, name='logout'),
    
    # Profile URLs
    path('profile/upload-photo/', views.upload_photo, name='upload_photo'),
    path('profile/delete-photo/<uuid:photo_id>/', views.delete_photo, name='delete_photo'),
    
    # Discovery and Matching URLs
    path('discover/', views.discover_profiles, name='discover'),
    path('like/', views.like_profile, name='like_profile'),
    path('matches/', views.get_matches, name='matches'),
    
    # Messaging URLs
    path('chat/<uuid:match_user_id>/', views.get_conversation, name='conversation'),
    path('send-message/', views.send_message, name='send_message'),
    path('message/<uuid:message_id>/read/', views.mark_message_read, name='mark_message_read'),

    # Subscription and Payment URLs
    path('subscription/', views.get_subscription, name='subscription'),
    path('payment/crypto/', views.create_crypto_payment, name='crypto_payment'),

    # Notification URLs
    path('notifications/', views.get_notifications, name='notifications'),
    path('notification/<uuid:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),

    # User Stats and Achievements
    path('stats/', views.get_user_stats, name='user_stats'),

    # Include router URLs
    path('', include(router.urls)),
]
