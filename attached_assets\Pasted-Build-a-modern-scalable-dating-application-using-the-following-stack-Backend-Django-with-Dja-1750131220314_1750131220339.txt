Build a modern, scalable dating application using the following stack:

- Backend: Django with Django REST Framework (DRF)
- Frontend: React Native (cross-platform mobile app)
- Database: PostgreSQL

### 💡 Key Features:

1. **User Authentication & Verification**
   - Sign up/login using email, phone, or social accounts (Google/Apple)
   - JWT-based token authentication
   - Email/phone OTP verification
   - Profile photo and selfie video verification

2. **User Profile System**
   - Create and edit profiles (bio, photos, gender, preferences, interests)
   - Upload multiple images with cropping
   - Add social links or voice notes

3. **Matching System**
   - Grid-based UI for profile discovery
   - Swipe-style or double-tap interaction to like
   - Mutual match triggers chat activation

4. **Real-Time Chat & Icebreakers**
   - One-to-one real-time messaging (WebSockets)
   - Typing indicators, unread message tracking
   - AI-generated conversation starters or icebreakers

5. **Location-Based Match Discovery**
   - Users can find matches within a specific radius
   - Option to set “preferred location” manually

6. **Premium Features for Monetization**
   - Paid subscription tiers (Gold, Platinum, etc.)
   - Boost profile visibility for 24h
   - See who liked you
   - Undo last swipe
   - AI match suggestions and advanced filters
   - In-app purchases using Stripe or Google/Apple Pay

7. **Admin Panel**
   - View/edit/delete users, reports, and flagged profiles
   - User banning and content moderation
   - Analytics dashboard (signups, retention, revenue)

8. **Security & Anti-Scam Features**
   - Report/block user feature
   - AI/machine learning to detect scam behavior or spam
   - Rate limiting, 2FA, and IP/device monitoring

9. **Deployment & DevOps**
   - Backend hosted on AWS or DigitalOcean
   - Use Docker for containerization
   - CI/CD pipeline setup with GitHub Actions
   - Media/images stored on AWS S3 or Cloudinary
   - Push notifications using Firebase Cloud Messaging (FCM)

### 🔌 Integration Notes:

- Use Django REST Framework for all APIs.
- PostgreSQL as the main database.
- React Native frontend must consume all APIs securely with token handling.
- Chat must use Django Channels (WebSockets).
- Ensure frontend uses modern design (tailored for dating UX).
- Push notifications must be included for matches, messages, and alerts.
