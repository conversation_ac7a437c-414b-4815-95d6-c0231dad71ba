#!/usr/bin/env python3
"""
Live API Test - Test the running Django server
"""

import requests
import json
import time

BASE_URL = 'http://localhost:8000'

def test_server_connection():
    """Test if server is running"""
    try:
        response = requests.get(f'{BASE_URL}/api/v1/', timeout=5)
        print(f"✓ Server is running: Status {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("✗ Server is not running")
        return False
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False

def test_user_registration():
    """Test user registration endpoint"""
    try:
        import time
        # Test data with unique email
        user_data = {
            'email': f'apitest{int(time.time())}@heartgrid.com',
            'name': 'API Test User',
            'password': 'testpass123',
            'password_confirm': 'testpass123',
            'date_of_birth': '1990-01-01'
        }
        
        response = requests.post(
            f'{BASE_URL}/api/v1/auth/register/',
            json=user_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Registration response: {response.status_code}")
        
        if response.status_code in [200, 201]:
            data = response.json()
            token = data.get('token')
            if token:
                print(f"✓ Registration successful, token: {token[:20]}...")
                return token
            else:
                print(f"✗ No token in response: {data}")
                return None
        else:
            print(f"✗ Registration failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Registration error: {e}")
        return None

def test_authenticated_endpoints(token):
    """Test endpoints that require authentication"""
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test profile endpoint
        response = requests.get(f'{BASE_URL}/api/v1/profiles/', headers=headers)
        print(f"Profiles endpoint: {response.status_code}")

        if response.status_code == 200:
            print("✓ Profiles endpoint working")
        else:
            print(f"✗ Profiles endpoint error: {response.text[:200]}...")
        
        # Test discover endpoint
        response = requests.get(f'{BASE_URL}/api/v1/discover/', headers=headers)
        print(f"Discover endpoint: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Discover endpoint working")
        else:
            print(f"✗ Discover endpoint error: {response.text}")
        
        # Test matches endpoint
        response = requests.get(f'{BASE_URL}/api/v1/matches/', headers=headers)
        print(f"Matches endpoint: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Matches endpoint working")
        else:
            print(f"✗ Matches endpoint error: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"✗ Authenticated endpoints error: {e}")
        return False

def test_premium_endpoints(token):
    """Test premium feature endpoints"""
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # Test subscription status
        response = requests.get(f'{BASE_URL}/api/v1/subscription/', headers=headers)
        print(f"Subscription endpoint: {response.status_code}")
        
        # Test crypto payment endpoints
        response = requests.get(f'{BASE_URL}/api/v1/payment/chains/', headers=headers)
        print(f"Crypto chains endpoint: {response.status_code}")

        # Test gamification endpoints
        response = requests.get(f'{BASE_URL}/api/v1/stats/', headers=headers)
        print(f"Stats endpoint: {response.status_code}")

        response = requests.get(f'{BASE_URL}/api/v1/achievements/', headers=headers)
        print(f"Achievements endpoint: {response.status_code}")

        response = requests.get(f'{BASE_URL}/api/v1/leaderboard/', headers=headers)
        print(f"Leaderboard endpoint: {response.status_code}")
        
        print("✓ Premium endpoints tested")
        return True
        
    except Exception as e:
        print(f"✗ Premium endpoints error: {e}")
        return False

def main():
    print("=" * 60)
    print("HeartGrid Live API Test")
    print("=" * 60)
    
    # Test 1: Server connection
    if not test_server_connection():
        print("\n❌ Server is not running. Please start with: python manage.py runserver")
        return False
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    # Test 2: User registration
    token = test_user_registration()
    if not token:
        print("\n❌ Registration failed")
        return False
    
    # Test 3: Authenticated endpoints
    if not test_authenticated_endpoints(token):
        print("\n❌ Authenticated endpoints failed")
        return False
    
    # Test 4: Premium endpoints
    if not test_premium_endpoints(token):
        print("\n❌ Premium endpoints failed")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 All API tests passed! Django REST API is working correctly.")
    print("=" * 60)
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        exit(1)
