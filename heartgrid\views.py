"""
HeartGrid Django Views

This module contains all the API views for the HeartGrid dating platform.
Converted from Flask routes to Django REST Framework views.
"""

from rest_framework import status, generics, permissions, viewsets
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
import uuid

from .models import (
    User, Profile, Photo, Like, Match, Message,
    Subscription, CryptoPayment, Notification,
    UserActivity, Achievement
)
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    ProfileSerializer, PhotoSerializer, LikeSerializer, MatchSerializer,
    MessageSerializer, SubscriptionSerializer, NotificationSerializer,
    AchievementSerializer, UserActivitySerializer, CryptoPaymentSerializer
)


# Authentication Views
class UserRegistrationView(generics.CreateAPIView):
    """
    User registration endpoint
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Create auth token
        token, created = Token.objects.get_or_create(user=user)

        # Log user activity
        UserActivity.objects.create(
            user=user,
            activity_type='login',
            metadata={'registration': True}
        )

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': 'Welcome to HeartGrid! Please complete your profile.'
        }, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([AllowAny])
def user_login(request):
    """
    User login endpoint
    """
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']

        # Create or get auth token
        token, created = Token.objects.get_or_create(user=user)

        # Log user activity
        UserActivity.objects.create(
            user=user,
            activity_type='login',
            metadata={'login_time': timezone.now().isoformat()}
        )

        return Response({
            'user': UserSerializer(user).data,
            'token': token.key,
            'message': f'Welcome back, {user.name}!'
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def user_logout(request):
    """
    User logout endpoint
    """
    try:
        # Delete the user's token
        request.user.auth_token.delete()
        return Response({'message': 'Successfully logged out.'})
    except:
        return Response({'message': 'Logout successful.'})


# Profile Views
class ProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet for user profiles
    """
    serializer_class = ProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Profile.objects.filter(user=self.request.user)

    def get_object(self):
        return get_object_or_404(Profile, user=self.request.user)

    def update(self, request, *args, **kwargs):
        response = super().update(request, *args, **kwargs)

        # Log profile update activity
        UserActivity.objects.create(
            user=request.user,
            activity_type='profile_updated',
            metadata={'updated_fields': list(request.data.keys())}
        )

        return response


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_photo(request):
    """
    Upload profile photo endpoint
    """
    if 'photo' not in request.FILES:
        return Response({'error': 'No photo provided'}, status=status.HTTP_400_BAD_REQUEST)

    profile = get_object_or_404(Profile, user=request.user)

    # Create photo instance
    photo = Photo.objects.create(
        profile=profile,
        image=request.FILES['photo'],
        is_primary=not profile.photos.exists()  # First photo is primary
    )

    # Log photo upload activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='photo_uploaded',
        metadata={'photo_id': str(photo.id)}
    )

    return Response({
        'photo': PhotoSerializer(photo).data,
        'message': 'Photo uploaded successfully!'
    })


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_photo(request, photo_id):
    """
    Delete profile photo endpoint
    """
    photo = get_object_or_404(Photo, id=photo_id, profile__user=request.user)
    photo.delete()

    return Response({'message': 'Photo deleted successfully!'})


# Discovery and Matching Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def discover_profiles(request):
    """
    Get discoverable profiles for the current user
    """
    user = request.user
    user_profile = get_object_or_404(Profile, user=user)

    # Get users already liked by current user
    liked_user_ids = Like.objects.filter(liker=user).values_list('liked_id', flat=True)

    # Build query for discoverable profiles
    queryset = Profile.objects.filter(
        is_visible=True,
        is_complete=True
    ).exclude(
        user=user
    ).exclude(
        user_id__in=liked_user_ids
    ).select_related('user').prefetch_related('photos')

    # Apply gender filtering if preferences are set
    if user_profile.interested_in and user_profile.interested_in != 'everyone':
        queryset = queryset.filter(gender=user_profile.interested_in)

    # Limit results
    limit = int(request.GET.get('limit', 20))
    profiles = queryset[:limit]

    return Response({
        'profiles': ProfileSerializer(profiles, many=True).data,
        'count': len(profiles)
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def like_profile(request):
    """
    Like a user profile
    """
    liked_user_id = request.data.get('user_id')
    if not liked_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        liked_user = User.objects.get(id=liked_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Check if already liked
    if Like.objects.filter(liker=request.user, liked=liked_user).exists():
        return Response({'error': 'Already liked this user'}, status=status.HTTP_400_BAD_REQUEST)

    # Create like
    like = Like.objects.create(
        liker=request.user,
        liked=liked_user,
        is_super_like=request.data.get('is_super_like', False)
    )

    # Check for mutual like (match)
    mutual_like = Like.objects.filter(liker=liked_user, liked=request.user).first()
    is_match = bool(mutual_like)
    match_id = None

    if is_match:
        # Create match
        match = Match.objects.create(
            user1=request.user,
            user2=liked_user
        )
        match_id = str(match.id)

        # Log match activities
        UserActivity.objects.create(
            user=request.user,
            activity_type='match_created',
            metadata={'match_id': match_id, 'matched_with': str(liked_user.id)}
        )
        UserActivity.objects.create(
            user=liked_user,
            activity_type='match_created',
            metadata={'match_id': match_id, 'matched_with': str(request.user.id)}
        )

        # Create notifications
        Notification.objects.create(
            user=request.user,
            notification_type='match',
            title='New Match!',
            message=f'You matched with {liked_user.name}',
            related_user=liked_user,
            related_match=match
        )
        Notification.objects.create(
            user=liked_user,
            notification_type='match',
            title='New Match!',
            message=f'You matched with {request.user.name}',
            related_user=request.user,
            related_match=match
        )

    # Log like activity
    activity_type = 'super_like_sent' if like.is_super_like else 'like_sent'
    UserActivity.objects.create(
        user=request.user,
        activity_type=activity_type,
        metadata={'liked_user': str(liked_user.id)}
    )

    # Create notification for liked user
    notification_type = 'super_like' if like.is_super_like else 'like'
    title = 'Someone super liked you!' if like.is_super_like else 'Someone liked you!'
    Notification.objects.create(
        user=liked_user,
        notification_type=notification_type,
        title=title,
        message=f'{request.user.name} {"super " if like.is_super_like else ""}liked you',
        related_user=request.user
    )

    response_data = {
        'success': True,
        'is_match': is_match,
        'is_super_like': like.is_super_like
    }

    if is_match:
        response_data.update({
            'match_name': liked_user.name,
            'match_id': match_id
        })

    return Response(response_data)


# Match and Messaging Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_matches(request):
    """
    Get user's matches
    """
    matches = Match.objects.filter(
        Q(user1=request.user) | Q(user2=request.user),
        is_active=True
    ).select_related('user1', 'user2').prefetch_related(
        'user1__profile', 'user2__profile'
    )

    match_data = []
    for match in matches:
        other_user = match.user2 if match.user1 == request.user else match.user1
        match_data.append({
            'match_id': str(match.id),
            'user': UserSerializer(other_user).data,
            'profile': ProfileSerializer(other_user.profile).data,
            'created_at': match.created_at
        })

    return Response({'matches': match_data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_conversation(request, match_user_id):
    """
    Get conversation with a matched user
    """
    try:
        match_user = User.objects.get(id=match_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Verify they are matched
    match = Match.objects.filter(
        Q(user1=request.user, user2=match_user) | Q(user1=match_user, user2=request.user),
        is_active=True
    ).first()

    if not match:
        return Response({'error': 'Not matched with this user'}, status=status.HTTP_403_FORBIDDEN)

    # Get messages
    messages = Message.objects.filter(
        match=match,
        is_deleted=False
    ).select_related('sender', 'receiver').order_by('created_at')

    return Response({
        'match': MatchSerializer(match).data,
        'messages': MessageSerializer(messages, many=True).data,
        'match_user': UserSerializer(match_user).data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_message(request):
    """
    Send a message to a matched user
    """
    match_user_id = request.data.get('user_id')
    content = request.data.get('content', '').strip()
    message_type = request.data.get('message_type', 'text')

    if not match_user_id:
        return Response({'error': 'User ID required'}, status=status.HTTP_400_BAD_REQUEST)

    if not content and message_type == 'text':
        return Response({'error': 'Message content required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        match_user = User.objects.get(id=match_user_id)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    # Verify they are matched
    match = Match.objects.filter(
        Q(user1=request.user, user2=match_user) | Q(user1=match_user, user2=request.user),
        is_active=True
    ).first()

    if not match:
        return Response({'error': 'Not matched with this user'}, status=status.HTTP_403_FORBIDDEN)

    # Create message
    message = Message.objects.create(
        match=match,
        sender=request.user,
        receiver=match_user,
        message_type=message_type,
        content=content
    )

    # Log message activity
    UserActivity.objects.create(
        user=request.user,
        activity_type='message_sent',
        metadata={'message_id': str(message.id), 'receiver': str(match_user.id)}
    )

    # Create notification for receiver
    Notification.objects.create(
        user=match_user,
        notification_type='message',
        title='New Message',
        message=f'{request.user.name} sent you a message',
        related_user=request.user,
        related_match=match
    )

    return Response({
        'message': MessageSerializer(message).data,
        'success': True
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_message_read(request, message_id):
    """
    Mark a message as read
    """
    try:
        message = Message.objects.get(id=message_id, receiver=request.user)
        if not message.is_read:
            message.is_read = True
            message.read_at = timezone.now()
            message.save()

        return Response({'success': True})
    except Message.DoesNotExist:
        return Response({'error': 'Message not found'}, status=status.HTTP_404_NOT_FOUND)


# Subscription and Payment Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_subscription(request):
    """
    Get user's current subscription
    """
    try:
        subscription = Subscription.objects.get(user=request.user)
        return Response({
            'subscription': SubscriptionSerializer(subscription).data,
            'is_active': subscription.is_active()
        })
    except Subscription.DoesNotExist:
        return Response({'error': 'No subscription found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_crypto_payment(request):
    """
    Create a cryptocurrency payment for subscription
    """
    plan = request.data.get('plan')
    chain = request.data.get('chain')
    token_type = request.data.get('token_type', 'native')

    if not plan or not chain:
        return Response({'error': 'Plan and chain required'}, status=status.HTTP_400_BAD_REQUEST)

    # Define pricing (in USD)
    pricing = {
        'weekly': 9.99,
        'fortnightly': 17.99,
        'monthly': 29.99
    }

    if plan not in pricing:
        return Response({'error': 'Invalid plan'}, status=status.HTTP_400_BAD_REQUEST)

    amount_usd = pricing[plan]

    # Create payment record
    payment = CryptoPayment.objects.create(
        user=request.user,
        subscription_plan=plan,
        chain=chain,
        token_type=token_type,
        payment_address='0x1234567890abcdef',  # This would be generated dynamically
        amount_crypto=0.001,  # This would be calculated based on current rates
        amount_usd=amount_usd,
        expires_at=timezone.now() + timedelta(hours=1)
    )

    return Response({
        'payment': CryptoPaymentSerializer(payment).data,
        'message': 'Payment created. Please send the exact amount to the provided address.'
    })


# Notification Views
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notifications(request):
    """
    Get user's notifications
    """
    notifications = Notification.objects.filter(user=request.user)[:20]
    return Response({
        'notifications': NotificationSerializer(notifications, many=True).data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_notification_read(request, notification_id):
    """
    Mark a notification as read
    """
    try:
        notification = Notification.objects.get(id=notification_id, user=request.user)
        if not notification.is_read:
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()

        return Response({'success': True})
    except Notification.DoesNotExist:
        return Response({'error': 'Notification not found'}, status=status.HTTP_404_NOT_FOUND)


# User Activity and Achievements
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_stats(request):
    """
    Get user statistics and achievements
    """
    user = request.user

    # Calculate stats
    likes_sent = Like.objects.filter(liker=user).count()
    likes_received = Like.objects.filter(liked=user).count()
    matches_count = Match.objects.filter(
        Q(user1=user) | Q(user2=user), is_active=True
    ).count()
    messages_sent = Message.objects.filter(sender=user).count()

    # Get achievements
    achievements = Achievement.objects.filter(user=user)

    return Response({
        'stats': {
            'likes_sent': likes_sent,
            'likes_received': likes_received,
            'matches': matches_count,
            'messages_sent': messages_sent,
            'profile_complete': user.profile.is_complete if hasattr(user, 'profile') else False
        },
        'achievements': AchievementSerializer(achievements, many=True).data
    })
