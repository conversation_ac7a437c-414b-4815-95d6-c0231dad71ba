#!/usr/bin/env python
"""
Quick diagnostic test to identify issues
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import Client
from django.urls import reverse, NoReverseMatch
from heartgrid.models import User, Profile

def test_url_patterns():
    """Test if URL patterns are working"""
    print("🔗 Testing URL Patterns...")
    
    client = Client()
    
    # Test basic URLs
    urls_to_test = [
        '/',
        '/register/',
        '/login/',
        '/dashboard/',
        '/profile/',
        '/matches/',
        '/messages/',
        '/subscription/',
        '/admin/',
        '/api/profiles/',
        '/api/matches/',
        '/api/messages/'
    ]
    
    for url in urls_to_test:
        try:
            response = client.get(url)
            status = "✅" if response.status_code in [200, 302, 403] else "❌"
            print(f"   {status} {url}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url}: ERROR - {e}")

def test_database():
    """Test database connectivity"""
    print("\n🗄️  Testing Database...")
    
    try:
        user_count = User.objects.count()
        profile_count = Profile.objects.count()
        print(f"   ✅ Users: {user_count}")
        print(f"   ✅ Profiles: {profile_count}")
        return True
    except Exception as e:
        print(f"   ❌ Database Error: {e}")
        return False

def test_templates():
    """Test if templates exist"""
    print("\n🎨 Testing Templates...")
    
    import os
    template_dir = 'templates'
    
    if os.path.exists(template_dir):
        templates = os.listdir(template_dir)
        print(f"   📁 Found {len(templates)} templates:")
        for template in templates:
            print(f"      - {template}")
        return True
    else:
        print(f"   ❌ Template directory not found: {template_dir}")
        return False

if __name__ == '__main__':
    print("🔍 HeartGrid Diagnostic Test")
    print("=" * 40)
    
    db_ok = test_database()
    template_ok = test_templates()
    test_url_patterns()
    
    print(f"\n📊 Summary:")
    print(f"   Database: {'✅' if db_ok else '❌'}")
    print(f"   Templates: {'✅' if template_ok else '❌'}")
