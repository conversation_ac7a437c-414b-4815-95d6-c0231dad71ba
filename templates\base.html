<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HeartGrid - Find Your Perfect Match{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="{{ url_for('index') }}">
                <i class="fas fa-heart text-danger me-2"></i>
                HeartGrid
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if session.user_id %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('discover') }}">
                                <i class="fas fa-compass me-1"></i>Discover
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('matches') }}">
                                <i class="fas fa-heart me-1"></i>Matches
                            </a>
                        </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.user_id %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ session.user_name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-edit me-2"></i>Edit Profile
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('subscription') }}">
                                    <i class="fas fa-crown me-2"></i>Subscription
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('gamification_dashboard') }}">
                                    <i class="fas fa-trophy me-2"></i>Achievements
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('notifications') }}">
                                    <i class="fas fa-bell me-2"></i>Notifications
                                    <span id="notificationBadge" class="badge bg-danger ms-1" style="display: none;"></span>
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('priority_support') }}">
                                    <i class="fas fa-headset me-2"></i>Support
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="{{ url_for('register') }}">Sign Up</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <div class="row">
                <div class="col-md-12">
                    <p class="text-muted mb-2">
                        <i class="fas fa-heart text-danger me-2"></i>
                        Made with love by HeartGrid
                    </p>
                    <p class="text-muted small">
                        &copy; 2025 HeartGrid. Find your perfect match.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <!-- Modern Notification System -->
    <script>
    function showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        }[type] || 'fa-info-circle';
        
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${icon} notification-icon"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="closeNotification(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto-dismiss
        setTimeout(() => {
            closeNotification(notification.querySelector('.notification-close'));
        }, duration);
    }
    
    function closeNotification(button) {
        const notification = button.closest('.notification');
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }
    
    // Check subscription status on page load
    document.addEventListener('DOMContentLoaded', function() {
        if ({{ 'true' if session.user_id else 'false' }}) {
            checkSubscriptionStatus();
        }
    });
    
    function checkSubscriptionStatus() {
        fetch('/subscription_status')
            .then(response => response.json())
            .then(data => {
                if (!data.can_chat && data.active === false) {
                    showNotification('Your trial has expired! Subscribe to continue chatting.', 'warning', 8000);
                }
            })
            .catch(error => console.log('Could not check subscription status'));
    }
    </script>
    
    <style>
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
    }
    
    .notification {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        margin-bottom: 10px;
        transform: translateX(100%);
        transition: all 0.3s ease;
        opacity: 0;
        border-left: 4px solid;
        max-width: 100%;
        word-wrap: break-word;
    }
    
    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }
    
    .notification-success { border-left-color: #28a745; }
    .notification-error { border-left-color: #dc3545; }
    .notification-warning { border-left-color: #ffc107; }
    .notification-info { border-left-color: #17a2b8; }
    
    .notification-content {
        display: flex;
        align-items: center;
        padding: 16px;
        gap: 12px;
    }
    
    .notification-icon {
        font-size: 18px;
        flex-shrink: 0;
    }
    
    .notification-success .notification-icon { color: #28a745; }
    .notification-error .notification-icon { color: #dc3545; }
    .notification-warning .notification-icon { color: #ffc107; }
    .notification-info .notification-icon { color: #17a2b8; }
    
    .notification-message {
        flex: 1;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        flex-shrink: 0;
    }
    
    .notification-close:hover {
        background: #f8f9fa;
        color: #495057;
    }
    
    @media (max-width: 768px) {
        .notification-container {
            left: 20px;
            right: 20px;
            max-width: none;
        }
    }
    </style>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
