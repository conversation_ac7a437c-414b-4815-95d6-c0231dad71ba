#!/usr/bin/env python
"""
Final System Test for HeartGrid Django Application

This script performs comprehensive testing of all major functionality
to verify the migration from Flask to Django was successful.
"""

import os
import django
import requests
import json
from datetime import date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from heartgrid.models import User, Profile, Match, Message, Subscription
from django.test import Client
from django.urls import reverse

def test_database_integrity():
    """Test database models and relationships"""
    print("🗄️  Testing Database Integrity...")
    
    # Check model counts
    user_count = User.objects.count()
    profile_count = Profile.objects.count()
    match_count = Match.objects.count()
    message_count = Message.objects.count()
    subscription_count = Subscription.objects.count()
    
    print(f"   📊 Database Statistics:")
    print(f"      Users: {user_count}")
    print(f"      Profiles: {profile_count}")
    print(f"      Matches: {match_count}")
    print(f"      Messages: {message_count}")
    print(f"      Subscriptions: {subscription_count}")
    
    # Test relationships
    users_without_profiles = User.objects.filter(profile__isnull=True).count()
    users_without_subscriptions = User.objects.filter(subscription__isnull=True).count()
    
    print(f"   🔗 Relationship Integrity:")
    print(f"      Users without profiles: {users_without_profiles}")
    print(f"      Users without subscriptions: {users_without_subscriptions}")
    
    # Test sample user
    if user_count > 0:
        sample_user = User.objects.first()
        print(f"   👤 Sample User: {sample_user.name} ({sample_user.email})")
        if hasattr(sample_user, 'profile'):
            print(f"      Profile: {sample_user.profile.location}")
            print(f"      Interests: {sample_user.profile.interests}")
        if hasattr(sample_user, 'subscription'):
            print(f"      Subscription: {sample_user.subscription.plan}")
    
    return user_count > 0 and profile_count > 0

def test_api_endpoints():
    """Test Django REST API endpoints"""
    print("\n🌐 Testing API Endpoints...")
    
    client = Client()
    
    # Test registration endpoint
    try:
        response = client.post('/api/register/', {
            'email': '<EMAIL>',
            'name': 'API Test User',
            'password': 'testpass123',
            'date_of_birth': '1990-01-01',
            'age': 34
        })
        print(f"   ✅ Registration API: {response.status_code}")
        
        if response.status_code == 201:
            # Try to get the created user
            user = User.objects.get(email='<EMAIL>')
            print(f"      Created user: {user.name}")
            
            # Test login
            login_response = client.post('/api/login/', {
                'email': '<EMAIL>',
                'password': 'testpass123'
            })
            print(f"   ✅ Login API: {login_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Registration/Login API Error: {e}")
    
    # Test profile endpoints
    try:
        response = client.get('/api/profiles/')
        print(f"   ✅ Profiles List API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Profiles API Error: {e}")
    
    # Test matches endpoint
    try:
        response = client.get('/api/matches/')
        print(f"   ✅ Matches API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Matches API Error: {e}")
    
    # Test messages endpoint
    try:
        response = client.get('/api/messages/')
        print(f"   ✅ Messages API: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Messages API Error: {e}")

def test_frontend_pages():
    """Test frontend page rendering"""
    print("\n🎨 Testing Frontend Pages...")
    
    client = Client()
    
    pages_to_test = [
        ('/', 'Home Page'),
        ('/register/', 'Registration Page'),
        ('/login/', 'Login Page'),
        ('/dashboard/', 'Dashboard'),
        ('/profile/', 'Profile Page'),
        ('/matches/', 'Matches Page'),
        ('/messages/', 'Messages Page'),
        ('/subscription/', 'Subscription Page'),
        ('/admin/', 'Admin Page')
    ]
    
    for url, name in pages_to_test:
        try:
            response = client.get(url)
            status = "✅" if response.status_code in [200, 302] else "❌"
            print(f"   {status} {name}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name} Error: {e}")

def test_authentication_system():
    """Test authentication functionality"""
    print("\n🔐 Testing Authentication System...")
    
    try:
        # Test user creation
        test_email = '<EMAIL>'
        
        # Clean up if exists
        User.objects.filter(email=test_email).delete()
        
        # Create user
        user = User.objects.create_user(
            email=test_email,
            name='Auth Test User',
            password='testpass123',
            date_of_birth=date(1990, 1, 1),
            age=34
        )
        print(f"   ✅ User Creation: {user.name}")
        
        # Test authentication
        authenticated = user.check_password('testpass123')
        print(f"   ✅ Password Authentication: {authenticated}")
        
        # Test profile creation
        profile = Profile.objects.create(
            user=user,
            bio='Test bio for authentication',
            gender='male',
            interested_in='female',
            location='Cape Town, South Africa',
            is_complete=True,
            is_visible=True
        )
        print(f"   ✅ Profile Creation: {profile.id}")
        
        # Test subscription creation
        subscription = Subscription.objects.create(
            user=user,
            plan='trial',
            status='active',
            features=['basic_chat']
        )
        print(f"   ✅ Subscription Creation: {subscription.plan}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Authentication Error: {e}")
        return False

def test_matching_system():
    """Test matching functionality"""
    print("\n💕 Testing Matching System...")
    
    try:
        # Get users for matching
        users = list(User.objects.all()[:2])
        
        if len(users) >= 2:
            user1, user2 = users[0], users[1]
            
            # Create a match
            match = Match.objects.create(
                user1=user1,
                user2=user2
            )
            print(f"   ✅ Match Creation: {user1.name} & {user2.name}")
            
            # Test match methods
            other_user = match.get_other_user(user1)
            print(f"   ✅ Match Method: Other user is {other_user.name}")
            
            # Create a message
            message = Message.objects.create(
                match=match,
                sender=user1,
                receiver=user2,
                content="Test message for matching system"
            )
            print(f"   ✅ Message Creation: {message.content[:30]}...")
            
            return True
        else:
            print("   ⚠️  Need at least 2 users for matching test")
            return False
            
    except Exception as e:
        print(f"   ❌ Matching Error: {e}")
        return False

def test_subscription_system():
    """Test subscription and payment functionality"""
    print("\n💳 Testing Subscription System...")
    
    try:
        # Test subscription plans
        plans = ['trial', 'weekly', 'fortnightly', 'monthly']
        
        for plan in plans:
            subscriptions = Subscription.objects.filter(plan=plan)
            print(f"   📊 {plan.title()} subscriptions: {subscriptions.count()}")
        
        # Test premium features
        premium_users = Subscription.objects.filter(
            status='active',
            plan__in=['weekly', 'fortnightly', 'monthly']
        ).count()
        print(f"   ✅ Premium Users: {premium_users}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Subscription Error: {e}")
        return False

def generate_test_report():
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("📋 FINAL TEST REPORT")
    print("=" * 60)
    
    # Run all tests
    db_ok = test_database_integrity()
    test_api_endpoints()
    test_frontend_pages()
    auth_ok = test_authentication_system()
    match_ok = test_matching_system()
    sub_ok = test_subscription_system()
    
    # Summary
    print(f"\n📊 Test Summary:")
    print(f"   Database Integrity: {'✅ PASS' if db_ok else '❌ FAIL'}")
    print(f"   Authentication: {'✅ PASS' if auth_ok else '❌ FAIL'}")
    print(f"   Matching System: {'✅ PASS' if match_ok else '❌ FAIL'}")
    print(f"   Subscription System: {'✅ PASS' if sub_ok else '❌ FAIL'}")
    
    overall_status = all([db_ok, auth_ok, match_ok, sub_ok])
    
    print(f"\n🎯 Overall Status: {'✅ ALL TESTS PASSED' if overall_status else '❌ SOME TESTS FAILED'}")
    
    if overall_status:
        print("\n🎉 HeartGrid Django Migration: SUCCESSFUL!")
        print("\n💡 The application is ready for:")
        print("   • Production deployment")
        print("   • User testing")
        print("   • Feature development")
        print("   • Performance optimization")
    else:
        print("\n⚠️  Some issues detected. Review test output above.")
    
    return overall_status

if __name__ == '__main__':
    print("🚀 HeartGrid Final System Test")
    print("Testing Django migration from Flask...")
    
    success = generate_test_report()
    
    if success:
        print("\n✅ All systems operational!")
    else:
        print("\n❌ Issues detected - review needed.")
