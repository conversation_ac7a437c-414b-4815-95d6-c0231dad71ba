import os
import uuid
from datetime import datetime
from flask import render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
from werkzeug.utils import secure_filename
from PIL import Image
from app import app
from models import data_store
from crypto_payment import crypto_processor
from premium_features import init_premium_features
from ai_matching import init_ai_matching
from gamification import init_gamification
from notifications import init_notifications
from rich_messaging import init_rich_messaging

# Initialize all systems
premium_features = init_premium_features(data_store)
ai_matching_engine = init_ai_matching(data_store)
gamification_engine = init_gamification(data_store)
notification_manager = init_notifications(data_store)
rich_messaging_manager = init_rich_messaging(data_store, app.config.get('UPLOAD_FOLDER', 'uploads'))

# Helper functions
def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def resize_image(image_path, max_size=(800, 800)):
    """Resize image to maximum dimensions while maintaining aspect ratio"""
    try:
        with Image.open(image_path) as img:
            # Convert RGBA to RGB if necessary
            if img.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = background
            
            # Resize maintaining aspect ratio
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(image_path, 'JPEG', quality=85, optimize=True)
            return True
    except Exception as e:
        app.logger.error(f"Error resizing image: {e}")
        return False

def login_required(f):
    """Decorator to require login for certain routes"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Routes
@app.route('/')
def index():
    """Home page"""
    if 'user_id' in session:
        return redirect(url_for('discover'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        name = request.form.get('name', '').strip()
        date_of_birth = request.form.get('date_of_birth', '')
        
        # Validation
        if not email or not password or not name or not date_of_birth:
            flash('All fields are required.', 'error')
            return render_template('register.html')
        
        # Validate name length
        if len(name) < 2 or len(name) > 50:
            flash('Name must be between 2 and 50 characters.', 'error')
            return render_template('register.html')
        
        # Validate password strength
        if len(password) < 8:
            flash('Password must be at least 8 characters long.', 'error')
            return render_template('register.html')
        
        if not any(c.islower() for c in password):
            flash('Password must contain at least one lowercase letter.', 'error')
            return render_template('register.html')
        
        if not any(c.isupper() for c in password):
            flash('Password must contain at least one uppercase letter.', 'error')
            return render_template('register.html')
        
        if not any(c.isdigit() for c in password):
            flash('Password must contain at least one number.', 'error')
            return render_template('register.html')
        
        # Validate age (must be 18+)
        try:
            from datetime import datetime, date
            birth_date = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            today = date.today()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            
            if age < 18:
                flash('You must be at least 18 years old to join HeartGrid.', 'error')
                return render_template('register.html')
            
            if age > 100:
                flash('Please enter a valid date of birth.', 'error')
                return render_template('register.html')
                
        except ValueError:
            flash('Please enter a valid date of birth.', 'error')
            return render_template('register.html')
        
        # Check if user already exists
        existing_user_id, existing_user = data_store.get_user_by_email(email)
        if existing_user:
            flash('An account with this email already exists.', 'error')
            return render_template('register.html')
        
        # Create new user
        user_id, user = data_store.create_user(email, password, name, birth_date)
        
        # Log the user in
        session['user_id'] = user_id
        session['user_name'] = name
        
        flash('Welcome to HeartGrid! Please complete your profile.', 'success')
        return redirect(url_for('profile'))
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        
        if not email or not password:
            flash('Email and password are required.', 'error')
            return render_template('login.html')
        
        # Find user
        user_id, user = data_store.get_user_by_email(email)
        if not user or not data_store.verify_password(user, password):
            flash('Invalid email or password.', 'error')
            return render_template('login.html')
        
        if not user.get('is_active', False):
            flash('Your account has been deactivated.', 'error')
            return render_template('login.html')
        
        # Log the user in
        session['user_id'] = user_id
        session['user_name'] = user['name']
        
        flash(f'Welcome back, {user["name"]}!', 'success')
        return redirect(url_for('discover'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """User logout"""
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile management"""
    user_id = session['user_id']
    user_profile = data_store.get_profile(user_id)
    
    if request.method == 'POST':
        # Handle form submission
        bio = request.form.get('bio', '').strip()
        age = request.form.get('age', '')
        gender = request.form.get('gender', '')
        interested_in = request.form.get('interested_in', '')
        location = request.form.get('location', '').strip()
        interests = request.form.getlist('interests')
        
        # Validation
        try:
            age = int(age) if age else None
            if age and (age < 18 or age > 100):
                flash('Age must be between 18 and 100.', 'error')
                return render_template('profile.html', profile=user_profile)
        except ValueError:
            flash('Please enter a valid age.', 'error')
            return render_template('profile.html', profile=user_profile)
        
        # Update profile
        profile_data = {
            'bio': bio,
            'age': age,
            'gender': gender,
            'interested_in': interested_in,
            'location': location,
            'interests': interests
        }
        
        if data_store.update_profile(user_id, profile_data):
            flash('Profile updated successfully!', 'success')
        else:
            flash('Failed to update profile.', 'error')
        
        return redirect(url_for('profile'))
    
    return render_template('profile.html', profile=user_profile)

@app.route('/upload_photo', methods=['POST'])
@login_required
def upload_photo():
    """Handle photo upload"""
    user_id = session['user_id']
    
    if 'photo' not in request.files:
        flash('No photo selected.', 'error')
        return redirect(url_for('profile'))
    
    file = request.files['photo']
    if file.filename == '':
        flash('No photo selected.', 'error')
        return redirect(url_for('profile'))
    
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = f"{user_id}_{uuid.uuid4().hex}.jpg"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        try:
            # Save and resize image
            file.save(filepath)
            if resize_image(filepath):
                # Add to user's photos
                user_profile = data_store.get_profile(user_id)
                if user_profile:
                    if 'photos' not in user_profile:
                        user_profile['photos'] = []
                    user_profile['photos'].append(filename)
                    data_store.update_profile(user_id, user_profile)
                    flash('Photo uploaded successfully!', 'success')
                else:
                    flash('Failed to update profile with photo.', 'error')
            else:
                os.remove(filepath)  # Remove failed upload
                flash('Failed to process image.', 'error')
        except Exception as e:
            app.logger.error(f"Error uploading photo: {e}")
            flash('Failed to upload photo.', 'error')
    else:
        flash('Invalid file type. Please upload JPG, PNG, GIF, or WebP images.', 'error')
    
    return redirect(url_for('profile'))

@app.route('/delete_photo/<photo_filename>')
@login_required
def delete_photo(photo_filename):
    """Delete a user's photo"""
    user_id = session['user_id']
    user_profile = data_store.get_profile(user_id)
    
    if user_profile and photo_filename in user_profile.get('photos', []):
        # Remove from profile
        user_profile['photos'].remove(photo_filename)
        data_store.update_profile(user_id, user_profile)
        
        # Delete file
        try:
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], photo_filename)
            if os.path.exists(filepath):
                os.remove(filepath)
        except Exception as e:
            app.logger.error(f"Error deleting photo file: {e}")
        
        flash('Photo deleted successfully!', 'success')
    else:
        flash('Photo not found.', 'error')
    
    return redirect(url_for('profile'))

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/discover')
@login_required
def discover():
    """Profile discovery page"""
    user_id = session['user_id']
    profiles = data_store.get_discoverable_profiles(user_id)
    return render_template('discover.html', profiles=profiles, data_store=data_store)

@app.route('/like_profile', methods=['POST'])
@login_required
def like_profile():
    """Like a profile (AJAX endpoint)"""
    user_id = session['user_id']
    
    # Handle both JSON and form data
    if request.is_json:
        liked_user_id = request.json.get('user_id') if request.json else None
    else:
        liked_user_id = request.form.get('user_id')
    
    if not liked_user_id:
        return jsonify({'success': False, 'error': 'Invalid user ID'})
    
    # Check if user exists
    if not data_store.get_user_by_id(liked_user_id):
        return jsonify({'success': False, 'error': 'User not found'})
    
    # Check daily like limit
    daily_limit = premium_features.get_daily_like_limit(user_id)
    if daily_limit != float('inf'):
        # Track activity for non-premium users
        today = datetime.now().date()
        activities = premium_features.daily_activities.get(user_id, {}).get(today, {})
        likes_today = activities.get('likes_sent', 0)
        
        if likes_today >= daily_limit:
            return jsonify({
                'success': False, 
                'error': f'Daily like limit reached ({daily_limit} per day). Upgrade for unlimited likes!'
            })
    
    # Like the user
    is_match, match_id = data_store.like_user(user_id, liked_user_id)
    
    # Track activity for premium features and gamification
    premium_features.track_activity(user_id, 'likes_sent')
    gamification_engine.track_user_action(user_id, 'like_sent')
    
    # Send notifications
    notification_manager.send_like_notification(liked_user_id, user_id, is_super_like=False)
    
    if is_match:
        gamification_engine.track_user_action(user_id, 'match_created')
        gamification_engine.track_user_action(liked_user_id, 'match_created')
        
        # Send match notifications to both users
        notification_manager.send_match_notification(user_id, liked_user_id)
        notification_manager.send_match_notification(liked_user_id, user_id)
    
    response = {
        'success': True,
        'is_match': is_match
    }
    
    if is_match:
        liked_user = data_store.get_user_by_id(liked_user_id)
        response['match_name'] = liked_user.get('name', 'Someone') if liked_user else 'Someone'
        response['match_id'] = match_id
    
    return jsonify(response)

@app.route('/matches')
@login_required
def matches():
    """View matches"""
    user_id = session['user_id']
    user_matches = data_store.get_matches(user_id)
    return render_template('matches.html', matches=user_matches, data_store=data_store)

@app.route('/chat/<match_user_id>')
@login_required
def chat(match_user_id):
    """Chat with a matched user"""
    user_id = session['user_id']
    
    # Verify they are matched
    user_matches = data_store.matches.get(user_id, set())
    if match_user_id not in user_matches:
        flash('You can only chat with matched users.', 'error')
        return redirect(url_for('matches'))
    
    # Get conversation and match profile
    conversation = data_store.get_conversation(user_id, match_user_id)
    match_profile = data_store.get_profile(match_user_id)
    match_user = data_store.get_user_by_id(match_user_id)
    
    if not match_profile or not match_user:
        flash('User not found.', 'error')
        return redirect(url_for('matches'))
    
    return render_template('chat.html', 
                         conversation=conversation,
                         match_profile=match_profile,
                         match_user=match_user,
                         match_user_id=match_user_id,
                         data_store=data_store)

@app.route('/send_message', methods=['POST'])
@login_required
def send_message():
    """Send a message (AJAX endpoint)"""
    user_id = session['user_id']
    
    # Check if user can send messages with premium restrictions
    can_send, error_msg = premium_features.can_send_message(user_id)
    if not can_send:
        return jsonify({
            'success': False, 
            'error': error_msg,
            'requires_subscription': True
        })
    
    # Handle both JSON and form data
    if request.is_json:
        receiver_id = request.json.get('receiver_id') if request.json else None
        message_text = request.json.get('message', '').strip() if request.json else ''
    else:
        receiver_id = request.form.get('receiver_id')
        message_text = request.form.get('message', '').strip()
    
    if not receiver_id or not message_text:
        return jsonify({'success': False, 'error': 'Invalid message data'})
    
    if len(message_text) > 1000:
        return jsonify({'success': False, 'error': 'Message too long'})
    
    # Send message
    if data_store.send_message(user_id, receiver_id, message_text):
        # Track message activity for premium features and gamification
        premium_features.track_activity(user_id, 'messages_sent')
        gamification_engine.track_user_action(user_id, 'message_sent')
        
        # Send notification to receiver
        notification_manager.send_message_notification(receiver_id, user_id, message_text)
        
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': 'Failed to send message'})

@app.route('/get_messages/<match_user_id>')
@login_required
def get_messages(match_user_id):
    """Get messages for a conversation (AJAX endpoint)"""
    user_id = session['user_id']
    
    # Verify they are matched
    user_matches = data_store.matches.get(user_id, set())
    if match_user_id not in user_matches:
        return jsonify({'success': False, 'error': 'Not matched'})
    
    conversation = data_store.get_conversation(user_id, match_user_id)
    
    # Format messages for JSON
    messages = []
    for msg in conversation:
        messages.append({
            'id': msg['id'],
            'sender_id': msg['sender_id'],
            'message': msg['message'],
            'timestamp': msg['timestamp'].isoformat(),
            'is_own': msg['sender_id'] == user_id
        })
    
    return jsonify({'success': True, 'messages': messages})

# Subscription Management Routes
@app.route('/subscription')
@login_required
def subscription():
    """Subscription management page"""
    user_id = session['user_id']
    subscription = data_store.get_subscription(user_id)
    has_premium = data_store.has_premium_features(user_id)
    can_chat = data_store.can_chat(user_id)
    
    return render_template('subscription.html', 
                         subscription=subscription,
                         has_premium=has_premium,
                         can_chat=can_chat)

@app.route('/crypto_subscription')
@login_required
def crypto_subscription():
    """Cryptocurrency subscription page"""
    return render_template('subscription_crypto.html')

@app.route('/create_crypto_payment', methods=['POST'])
@login_required
def create_crypto_payment():
    """Create a cryptocurrency payment request"""
    user_id = session['user_id']
    data = request.get_json()
    
    plan = data.get('plan')
    network = data.get('network')
    token = data.get('token')
    
    if not all([plan, network, token]):
        return jsonify({'success': False, 'error': 'Missing required parameters'})
    
    try:
        payment_request = crypto_processor.create_payment_request(
            user_id, plan, network, token
        )
        return jsonify({'success': True, 'payment': payment_request})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/check_crypto_payment', methods=['POST'])
@login_required
def check_crypto_payment():
    """Check cryptocurrency payment status"""
    data = request.get_json()
    payment_id = data.get('payment_id')
    
    if not payment_id:
        return jsonify({'success': False, 'error': 'Payment ID required'})
    
    try:
        payment_status = crypto_processor.check_payment_status(payment_id)
        
        if payment_status.get('status') == 'confirmed':
            # Activate subscription
            user_id = session['user_id']
            plan = payment_status.get('plan')
            if data_store.create_subscription(user_id, plan):
                return jsonify({
                    'status': 'confirmed',
                    'message': 'Payment confirmed and subscription activated'
                })
        
        return jsonify(payment_status)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/crypto_networks')
def crypto_networks():
    """Get supported cryptocurrency networks"""
    return jsonify(crypto_processor.get_supported_networks())

# Premium Feature Routes
@app.route('/premium_filters')
@login_required
def premium_filters():
    """Advanced search filters page"""
    user_id = session['user_id']
    has_premium_filters = premium_features.has_feature(user_id, 'premium_filters')
    max_radius = premium_features.get_match_radius(user_id)
    
    return render_template('premium_filters.html', 
                         has_premium_filters=has_premium_filters,
                         max_radius=max_radius)

@app.route('/profile_analytics')
@login_required
def profile_analytics():
    """Profile analytics page"""
    user_id = session['user_id']
    analytics = premium_features.get_user_analytics(user_id)
    
    return render_template('profile_analytics.html', analytics=analytics)

@app.route('/boost_profile', methods=['POST'])
@login_required
def boost_profile():
    """Boost user profile (premium feature)"""
    user_id = session['user_id']
    
    can_boost, error_msg = premium_features.can_boost_profile(user_id)
    if not can_boost:
        return jsonify({'success': False, 'error': error_msg})
    
    # Track boost activity
    premium_features.track_activity(user_id, 'profile_boosts')
    
    return jsonify({
        'success': True,
        'message': 'Profile boosted successfully! You\'ll appear higher in discovery for the next hour.'
    })

@app.route('/super_like', methods=['POST'])
@login_required
def super_like():
    """Send a super like (limited feature)"""
    user_id = session['user_id']
    data = request.get_json() if request.is_json else request.form
    liked_user_id = data.get('user_id')
    
    if not liked_user_id:
        return jsonify({'success': False, 'error': 'Invalid user ID'})
    
    can_super_like, error_msg = premium_features.can_super_like(user_id)
    if not can_super_like:
        return jsonify({'success': False, 'error': error_msg})
    
    # Check if user exists
    if not data_store.get_user_by_id(liked_user_id):
        return jsonify({'success': False, 'error': 'User not found'})
    
    # Send super like (same as regular like but tracked differently)
    is_match, match_id = data_store.like_user(user_id, liked_user_id)
    
    # Track super like activity for premium features and gamification
    premium_features.track_activity(user_id, 'super_likes')
    gamification_engine.track_user_action(user_id, 'super_like_sent')
    
    # Send super like notification
    notification_manager.send_like_notification(liked_user_id, user_id, is_super_like=True)
    
    if is_match:
        gamification_engine.track_user_action(user_id, 'match_created')
        gamification_engine.track_user_action(liked_user_id, 'match_created')
        
        # Send match notifications to both users
        notification_manager.send_match_notification(user_id, liked_user_id)
        notification_manager.send_match_notification(liked_user_id, user_id)
    
    response = {
        'success': True,
        'is_match': is_match,
        'is_super_like': True
    }
    
    if is_match:
        liked_user = data_store.get_user_by_id(liked_user_id)
        response['match_name'] = liked_user.get('name', 'Someone') if liked_user else 'Someone'
        response['match_id'] = match_id
    
    return jsonify(response)

@app.route('/read_receipts/<message_id>')
@login_required
def get_read_receipt(message_id):
    """Get read receipt for a message (premium feature)"""
    user_id = session['user_id']
    
    if not premium_features.can_see_read_receipts(user_id):
        return jsonify({'error': 'Read receipts require fortnightly or monthly subscription!'})
    
    # Mock read receipt data
    return jsonify({
        'message_id': message_id,
        'read': True,
        'read_at': '2024-12-24T10:30:00Z'
    })

@app.route('/priority_support')
@login_required
def priority_support():
    """Priority support information"""
    user_id = session['user_id']
    support_info = premium_features.get_priority_support_status(user_id)
    
    return render_template('support.html', support_info=support_info)

@app.route('/profile_details/<user_id>')
@login_required
def profile_details(user_id):
    """Get detailed profile information for modal display"""
    current_user_id = session['user_id']
    
    # Get user and profile data
    user = data_store.get_user_by_id(user_id)
    profile = data_store.get_profile(user_id)
    
    if not user or not profile:
        return jsonify({'success': False, 'error': 'Profile not found'})
    
    # Check if users are matched (for chat access)
    user_matches = data_store.get_matches(current_user_id)
    can_chat = any(match['user_id'] == user_id for match in user_matches)
    
    return jsonify({
        'success': True,
        'user': {
            'name': user.get('name', 'User'),
            'id': user_id
        },
        'profile': {
            'user_id': user_id,
            'bio': profile.get('bio', ''),
            'age': profile.get('age'),
            'gender': profile.get('gender', ''),
            'location': profile.get('location', ''),
            'interests': profile.get('interests', []),
            'photos': profile.get('photos', [])
        },
        'can_chat': can_chat
    })

# Gamification and Notification Routes
@app.route('/gamification')
@login_required
def gamification_dashboard():
    """Gamification dashboard"""
    user_id = session['user_id']
    
    gamification_data = gamification_engine.get_user_gamification_data(user_id)
    daily_challenge = gamification_engine.get_daily_challenge(user_id)
    leaderboard = gamification_engine.get_leaderboard()
    
    return render_template('gamification.html',
                         gamification_data=gamification_data,
                         daily_challenge=daily_challenge,
                         leaderboard=leaderboard)

@app.route('/notifications')
@login_required
def notifications():
    """User notifications page"""
    user_id = session['user_id']
    
    notifications = notification_manager.get_user_notifications(user_id)
    unread_count = notification_manager.get_unread_count(user_id)
    preferences = notification_manager.get_user_preferences(user_id)
    
    return render_template('notifications.html',
                         notifications=notifications,
                         unread_count=unread_count,
                         preferences=preferences)

@app.route('/notifications/mark_read/<notification_id>', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Mark notification as read"""
    user_id = session['user_id']
    success = notification_manager.mark_notification_read(user_id, notification_id)
    
    return jsonify({'success': success})

@app.route('/notifications/mark_all_read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read"""
    user_id = session['user_id']
    notification_manager.mark_all_notifications_read(user_id)
    
    return jsonify({'success': True})

@app.route('/notifications/preferences', methods=['POST'])
@login_required
def update_notification_preferences():
    """Update notification preferences"""
    user_id = session['user_id']
    preferences = request.get_json() if request.is_json else request.form
    
    notification_manager.update_user_preferences(user_id, preferences)
    
    return jsonify({'success': True})

@app.route('/api/notifications/unread_count')
@login_required
def get_unread_notification_count():
    """Get unread notification count (for badge)"""
    user_id = session['user_id']
    count = notification_manager.get_unread_count(user_id)
    
    return jsonify({'count': count})

@app.route('/ai_insights')
@login_required
def ai_insights():
    """AI-powered insights and recommendations"""
    user_id = session['user_id']
    
    # Get compatibility insights for recent profiles
    insights = {
        'compatibility_tips': [
            "Users with similar interests have 3x higher match rates",
            "Adding more photos increases your visibility by 40%",
            "Profiles with complete bios get 60% more matches"
        ],
        'optimal_activity_time': "8 PM - 10 PM (based on your activity pattern)",
        'profile_strength_score': 85,
        'improvement_suggestions': [
            "Add 2 more photos for better visibility",
            "Update your bio with recent interests",
            "Try super liking users with high compatibility scores"
        ]
    }
    
    return render_template('ai_insights.html', insights=insights)

# Rich Messaging Routes
@app.route('/upload_media', methods=['POST'])
@login_required
def upload_media():
    """Upload media file for messaging"""
    user_id = session['user_id']
    
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file provided'})
    
    file = request.files['file']
    media_type = request.form.get('type', 'images')  # images, voice, videos
    
    result = rich_messaging_manager.save_media_file(file, media_type, user_id)
    return jsonify(result)

@app.route('/send_media_message', methods=['POST'])
@login_required
def send_media_message():
    """Send message with media attachment"""
    user_id = session['user_id']
    
    # Check messaging permissions
    can_send, error_msg = premium_features.can_send_message(user_id)
    if not can_send:
        return jsonify({'success': False, 'error': error_msg})
    
    data = request.get_json()
    receiver_id = data.get('receiver_id')
    message_text = data.get('message', '')
    media_data = data.get('media')
    
    if not receiver_id or not media_data:
        return jsonify({'success': False, 'error': 'Missing required data'})
    
    success = rich_messaging_manager.send_media_message(user_id, receiver_id, media_data, message_text)
    
    if success:
        # Track activity
        gamification_engine.track_user_action(user_id, 'message_sent')
        notification_manager.send_message_notification(receiver_id, user_id, message_text or "Sent a photo")
        
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': 'Failed to send message'})

@app.route('/send_voice_message', methods=['POST'])
@login_required
def send_voice_message():
    """Send voice message"""
    user_id = session['user_id']
    
    # Check messaging permissions
    can_send, error_msg = premium_features.can_send_message(user_id)
    if not can_send:
        return jsonify({'success': False, 'error': error_msg})
    
    if 'voice' not in request.files:
        return jsonify({'success': False, 'error': 'No voice file provided'})
    
    voice_file = request.files['voice']
    receiver_id = request.form.get('receiver_id')
    duration = int(request.form.get('duration', 0))
    
    if not receiver_id:
        return jsonify({'success': False, 'error': 'Receiver ID required'})
    
    # Save voice file
    voice_result = rich_messaging_manager.save_media_file(voice_file, 'voice', user_id)
    
    if not voice_result['success']:
        return jsonify(voice_result)
    
    # Send voice message
    success = rich_messaging_manager.send_voice_message(user_id, receiver_id, voice_result, duration)
    
    if success:
        # Track activity
        gamification_engine.track_user_action(user_id, 'message_sent')
        notification_manager.send_message_notification(receiver_id, user_id, "Sent a voice message")
        
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'error': 'Failed to send voice message'})

@app.route('/message_reaction', methods=['POST'])
@login_required
def add_message_reaction():
    """Add reaction to message"""
    user_id = session['user_id']
    data = request.get_json()
    
    message_id = data.get('message_id')
    reaction = data.get('reaction')
    
    if not message_id or not reaction:
        return jsonify({'success': False, 'error': 'Missing data'})
    
    success = rich_messaging_manager.add_message_reaction(user_id, message_id, reaction)
    return jsonify({'success': success})

@app.route('/message_reaction', methods=['DELETE'])
@login_required
def remove_message_reaction():
    """Remove reaction from message"""
    user_id = session['user_id']
    data = request.get_json()
    
    message_id = data.get('message_id')
    reaction = data.get('reaction')
    
    if not message_id or not reaction:
        return jsonify({'success': False, 'error': 'Missing data'})
    
    success = rich_messaging_manager.remove_message_reaction(user_id, message_id, reaction)
    return jsonify({'success': success})

@app.route('/mark_message_read/<message_id>', methods=['POST'])
@login_required
def mark_message_read(message_id):
    """Mark message as read"""
    user_id = session['user_id']
    success = rich_messaging_manager.mark_message_read(user_id, message_id)
    return jsonify({'success': success})

@app.route('/delete_message/<message_id>', methods=['DELETE'])
@login_required
def delete_message(message_id):
    """Delete message"""
    user_id = session['user_id']
    success = rich_messaging_manager.delete_message(user_id, message_id)
    return jsonify({'success': success})

@app.route('/payment_options/<plan>')
@login_required
def payment_options(plan):
    """Payment options page for selected plan"""
    user_id = session['user_id']
    
    if plan not in ['weekly', 'fortnightly', 'monthly']:
        flash('Invalid subscription plan.', 'error')
        return redirect(url_for('subscription'))
    
    # Store selected plan in session
    session['selected_plan'] = plan
    
    # Get plan details
    plan_details = data_store.subscription_pricing.get(plan, {})
    
    return render_template('payment_options.html', 
                         plan=plan, 
                         plan_details=plan_details)

@app.route('/subscribe/<plan>', methods=['POST'])
@login_required
def subscribe(plan):
    """Subscribe to a plan"""
    user_id = session['user_id']
    
    # Validate plan
    valid_plans = ['weekly', 'fortnightly', 'monthly']
    if plan not in valid_plans:
        flash('Invalid subscription plan.', 'error')
        return redirect(url_for('subscription'))
    
    # Create subscription
    if data_store.create_subscription(user_id, plan):
        plan_names = {
            'weekly': 'Weekly',
            'fortnightly': 'Fortnightly', 
            'monthly': 'Monthly'
        }
        flash(f'Successfully subscribed to {plan_names[plan]} plan! You now have access to voice and video calls.', 'success')
    else:
        flash('Failed to create subscription. Please try again.', 'error')
    
    return redirect(url_for('subscription'))

@app.route('/subscription_status')
@login_required
def subscription_status():
    """Get subscription status (AJAX endpoint)"""
    user_id = session['user_id']
    subscription = data_store.get_subscription(user_id)
    
    if subscription:
        return jsonify({
            'active': data_store.is_subscription_active(user_id),
            'plan': subscription['plan'],
            'expires_at': subscription['expires_at'].isoformat(),
            'can_chat': data_store.can_chat(user_id),
            'has_premium': data_store.has_premium_features(user_id)
        })
    else:
        return jsonify({'active': False, 'can_chat': False, 'has_premium': False})

# Admin Dashboard Routes
@app.route('/admin')
@login_required
def admin_dashboard():
    """Admin dashboard"""
    user_id = session['user_id']
    
    if not data_store.is_admin(user_id):
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))
    
    stats = data_store.get_all_users_stats()
    return render_template('admin/dashboard.html', stats=stats)

@app.route('/admin/users')
@login_required
def admin_users():
    """Admin users management"""
    user_id = session['user_id']
    
    if not data_store.is_admin(user_id):
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('index'))
    
    users = []
    for uid, user in data_store.users.items():
        subscription = data_store.get_subscription(uid)
        users.append({
            'id': uid,
            'name': user['name'],
            'email': user['email'],
            'created_at': user['created_at'],
            'is_active': user['is_active'],
            'subscription': subscription
        })
    
    return render_template('admin/users.html', users=users)

@app.route('/admin/toggle_user/<user_id>', methods=['POST'])
@login_required
def admin_toggle_user(user_id):
    """Toggle user active status"""
    admin_id = session['user_id']
    
    if not data_store.is_admin(admin_id):
        return jsonify({'success': False, 'error': 'Access denied'})
    
    user = data_store.get_user_by_id(user_id)
    if user:
        user['is_active'] = not user.get('is_active', True)
        status = 'activated' if user['is_active'] else 'deactivated'
        return jsonify({'success': True, 'message': f'User {status} successfully'})
    else:
        return jsonify({'success': False, 'error': 'User not found'})

# Video/Voice Call Routes (Premium Features)
@app.route('/call/<match_user_id>')
@login_required
def video_call(match_user_id):
    """Video call page (premium feature)"""
    user_id = session['user_id']
    
    # Check if user can make video calls
    can_call, error_msg = premium_features.can_make_call(user_id, 'video')
    if not can_call:
        flash(error_msg, 'error')
        return redirect(url_for('subscription'))
    
    # Verify they are matched
    user_matches = data_store.get_matches(user_id)
    match_user = None
    for match in user_matches:
        if match['user_id'] == match_user_id:
            match_user = match
            break
    
    if not match_user:
        flash('You can only call matched users.', 'error')
        return redirect(url_for('matches'))
    
    return render_template('call.html', match_user=match_user, data_store=data_store)

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(413)
def too_large(error):
    flash('File too large. Please upload images smaller than 16MB.', 'error')
    return redirect(request.url)
