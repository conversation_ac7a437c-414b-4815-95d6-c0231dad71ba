"""
HeartGrid Frontend URL Configuration

This module contains URL patterns for frontend template views.
"""

from django.urls import path
from . import views

app_name = 'heartgrid_frontend'

urlpatterns = [
    # Landing page
    path('', views.index, name='index'),
    
    # Authentication pages
    path('login/', views.login_page, name='login_page'),
    path('register/', views.register_page, name='register_page'),
    
    # Main app pages (require authentication)
    path('discover/', views.discover_page, name='discover_page'),
    path('profile/', views.profile_page, name='profile_page'),
    path('matches/', views.matches_page, name='matches_page'),
    path('chat/', views.chat_page, name='chat_page'),
    path('chat/<uuid:match_user_id>/', views.chat_page, name='chat_page_with_user'),
    path('subscription/', views.subscription_page, name='subscription_page'),
    path('notifications/', views.notifications_page, name='notifications_page'),
    path('gamification/', views.gamification_page, name='gamification_page'),
]
