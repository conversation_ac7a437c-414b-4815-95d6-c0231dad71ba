"""
Rich Messaging System for HeartGrid
Enhanced messaging with media sharing, voice notes, and reactions
"""

import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from werkzeug.utils import secure_filename

class RichMessagingManager:
    """Manages enhanced messaging features"""
    
    def __init__(self, data_store, upload_folder):
        self.data_store = data_store
        self.upload_folder = upload_folder
        
        # Ensure media upload directories exist
        self.media_folders = {
            'images': os.path.join(upload_folder, 'messages', 'images'),
            'voice': os.path.join(upload_folder, 'messages', 'voice'),
            'videos': os.path.join(upload_folder, 'messages', 'videos')
        }
        
        for folder in self.media_folders.values():
            os.makedirs(folder, exist_ok=True)
        
        # Initialize message reactions if not exists
        if not hasattr(data_store, 'message_reactions'):
            data_store.message_reactions = {}
        
        # Initialize message read receipts
        if not hasattr(data_store, 'message_read_receipts'):
            data_store.message_read_receipts = {}
        
        # Allowed file types
        self.allowed_extensions = {
            'images': {'png', 'jpg', 'jpeg', 'gif', 'webp'},
            'voice': {'mp3', 'wav', 'ogg', 'm4a'},
            'videos': {'mp4', 'webm', 'mov'}
        }
        
        # File size limits (in bytes)
        self.size_limits = {
            'images': 10 * 1024 * 1024,  # 10MB
            'voice': 5 * 1024 * 1024,   # 5MB
            'videos': 50 * 1024 * 1024  # 50MB
        }
    
    def is_allowed_file(self, filename: str, media_type: str) -> bool:
        """Check if file type is allowed"""
        if '.' not in filename:
            return False
        
        extension = filename.rsplit('.', 1)[1].lower()
        return extension in self.allowed_extensions.get(media_type, set())
    
    def save_media_file(self, file, media_type: str, user_id: str) -> Dict:
        """Save uploaded media file"""
        if not file or not file.filename:
            return {'success': False, 'error': 'No file provided'}
        
        if not self.is_allowed_file(file.filename, media_type):
            return {'success': False, 'error': f'File type not allowed for {media_type}'}
        
        # Check file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > self.size_limits[media_type]:
            size_mb = self.size_limits[media_type] / (1024 * 1024)
            return {'success': False, 'error': f'File too large. Max size: {size_mb}MB'}
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{file_id}.{extension}"
        
        # Save file
        filepath = os.path.join(self.media_folders[media_type], filename)
        file.save(filepath)
        
        return {
            'success': True,
            'file_id': file_id,
            'filename': filename,
            'file_size': file_size,
            'file_path': f"messages/{media_type}/{filename}"
        }
    
    def send_media_message(self, sender_id: str, receiver_id: str, media_data: Dict, message_text: str = "") -> bool:
        """Send message with media attachment"""
        message_id = str(uuid.uuid4())
        
        message = {
            'id': message_id,
            'sender_id': sender_id,
            'receiver_id': receiver_id,
            'message': message_text,
            'media': media_data,
            'timestamp': datetime.now().isoformat(),
            'read': False,
            'type': 'media'
        }
        
        # Store message
        conversation_key = f"{min(sender_id, receiver_id)}_{max(sender_id, receiver_id)}"
        
        if conversation_key not in self.data_store.conversations:
            self.data_store.conversations[conversation_key] = []
        
        self.data_store.conversations[conversation_key].append(message)
        
        return True
    
    def send_voice_message(self, sender_id: str, receiver_id: str, voice_data: Dict, duration: int) -> bool:
        """Send voice message"""
        message_id = str(uuid.uuid4())
        
        message = {
            'id': message_id,
            'sender_id': sender_id,
            'receiver_id': receiver_id,
            'message': "",
            'voice': voice_data,
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'read': False,
            'type': 'voice'
        }
        
        # Store message
        conversation_key = f"{min(sender_id, receiver_id)}_{max(sender_id, receiver_id)}"
        
        if conversation_key not in self.data_store.conversations:
            self.data_store.conversations[conversation_key] = []
        
        self.data_store.conversations[conversation_key].append(message)
        
        return True
    
    def add_message_reaction(self, user_id: str, message_id: str, reaction: str) -> bool:
        """Add reaction to message"""
        if message_id not in self.data_store.message_reactions:
            self.data_store.message_reactions[message_id] = {}
        
        # Remove previous reaction from this user
        for existing_reaction in self.data_store.message_reactions[message_id]:
            if user_id in self.data_store.message_reactions[message_id][existing_reaction]:
                self.data_store.message_reactions[message_id][existing_reaction].remove(user_id)
                if not self.data_store.message_reactions[message_id][existing_reaction]:
                    del self.data_store.message_reactions[message_id][existing_reaction]
        
        # Add new reaction
        if reaction not in self.data_store.message_reactions[message_id]:
            self.data_store.message_reactions[message_id][reaction] = []
        
        if user_id not in self.data_store.message_reactions[message_id][reaction]:
            self.data_store.message_reactions[message_id][reaction].append(user_id)
        
        return True
    
    def remove_message_reaction(self, user_id: str, message_id: str, reaction: str) -> bool:
        """Remove reaction from message"""
        if (message_id in self.data_store.message_reactions and 
            reaction in self.data_store.message_reactions[message_id] and
            user_id in self.data_store.message_reactions[message_id][reaction]):
            
            self.data_store.message_reactions[message_id][reaction].remove(user_id)
            
            # Clean up empty reactions
            if not self.data_store.message_reactions[message_id][reaction]:
                del self.data_store.message_reactions[message_id][reaction]
            
            return True
        
        return False
    
    def get_message_reactions(self, message_id: str) -> Dict:
        """Get all reactions for a message"""
        return self.data_store.message_reactions.get(message_id, {})
    
    def mark_message_read(self, user_id: str, message_id: str) -> bool:
        """Mark message as read and create read receipt"""
        # Find and update the message
        for conversation in self.data_store.conversations.values():
            for message in conversation:
                if message['id'] == message_id and message['receiver_id'] == user_id:
                    message['read'] = True
                    
                    # Create read receipt
                    self.data_store.message_read_receipts[message_id] = {
                        'reader_id': user_id,
                        'read_at': datetime.now().isoformat()
                    }
                    
                    return True
        
        return False
    
    def get_read_receipt(self, message_id: str) -> Optional[Dict]:
        """Get read receipt for message"""
        return self.data_store.message_read_receipts.get(message_id)
    
    def get_enhanced_conversation(self, user1_id: str, user2_id: str) -> List[Dict]:
        """Get conversation with enhanced data (reactions, read receipts)"""
        conversation_key = f"{min(user1_id, user2_id)}_{max(user1_id, user2_id)}"
        messages = self.data_store.conversations.get(conversation_key, [])
        
        # Enhance messages with reactions and read receipts
        enhanced_messages = []
        for message in messages:
            enhanced_message = message.copy()
            
            # Add reactions
            enhanced_message['reactions'] = self.get_message_reactions(message['id'])
            
            # Add read receipt (only for sender)
            if message['sender_id'] == user1_id or message['sender_id'] == user2_id:
                read_receipt = self.get_read_receipt(message['id'])
                enhanced_message['read_receipt'] = read_receipt
            
            enhanced_messages.append(enhanced_message)
        
        return enhanced_messages
    
    def get_typing_status(self, user1_id: str, user2_id: str) -> Dict:
        """Get typing status for conversation (mock implementation)"""
        # In a real implementation, this would use WebSocket or Server-Sent Events
        return {
            'is_typing': False,
            'user_id': None,
            'started_at': None
        }
    
    def set_typing_status(self, user_id: str, conversation_id: str, is_typing: bool) -> bool:
        """Set typing status (mock implementation)"""
        # In a real implementation, this would broadcast to WebSocket clients
        return True
    
    def get_message_analytics(self, user_id: str) -> Dict:
        """Get messaging analytics for user"""
        total_sent = 0
        total_received = 0
        media_sent = 0
        voice_sent = 0
        reactions_received = 0
        
        # Count messages across all conversations
        for conversation in self.data_store.conversations.values():
            for message in conversation:
                if message['sender_id'] == user_id:
                    total_sent += 1
                    if message.get('type') == 'media':
                        media_sent += 1
                    elif message.get('type') == 'voice':
                        voice_sent += 1
                elif message['receiver_id'] == user_id:
                    total_received += 1
                
                # Count reactions received
                reactions = self.get_message_reactions(message['id'])
                if message['sender_id'] == user_id:
                    reactions_received += sum(len(users) for users in reactions.values())
        
        return {
            'total_sent': total_sent,
            'total_received': total_received,
            'media_sent': media_sent,
            'voice_sent': voice_sent,
            'reactions_received': reactions_received,
            'avg_response_time': "2.5 minutes",  # Mock data
            'most_active_time': "8 PM - 10 PM"
        }
    
    def delete_message(self, user_id: str, message_id: str) -> bool:
        """Delete message (only sender can delete)"""
        for conversation in self.data_store.conversations.values():
            for i, message in enumerate(conversation):
                if message['id'] == message_id and message['sender_id'] == user_id:
                    # Mark as deleted instead of removing (for conversation flow)
                    conversation[i]['deleted'] = True
                    conversation[i]['message'] = "This message was deleted"
                    conversation[i]['media'] = None
                    conversation[i]['voice'] = None
                    return True
        
        return False

# Global rich messaging manager
rich_messaging_manager = None

def init_rich_messaging(data_store, upload_folder):
    global rich_messaging_manager
    rich_messaging_manager = RichMessagingManager(data_store, upload_folder)
    return rich_messaging_manager