#!/usr/bin/env python
"""
Simple script to create sample matches and messages for existing users
"""

import os
import django
import random
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from heartgrid.models import User, Profile, Match, Message
from django.utils import timezone

def create_matches_and_messages():
    """Create sample matches and messages between existing users"""
    print("🚀 Creating sample matches and messages...")
    
    # Get all users with profiles
    users = list(User.objects.filter(profile__isnull=False))
    print(f"📊 Found {len(users)} users with profiles")
    
    if len(users) < 2:
        print("❌ Need at least 2 users to create matches")
        return
    
    # Create matches
    matches_created = 0
    for i in range(min(30, len(users) * 2)):  # Create up to 30 matches
        try:
            user1, user2 = random.sample(users, 2)
            
            # Check if match already exists
            if Match.objects.filter(
                user1=user1, user2=user2
            ).exists() or Match.objects.filter(
                user1=user2, user2=user1
            ).exists():
                continue
            
            # Create match (created_at is auto-set)
            match = Match.objects.create(
                user1=user1,
                user2=user2
            )
            matches_created += 1
            
        except Exception as e:
            print(f"   Error creating match: {e}")
            continue
    
    print(f"✅ Created {matches_created} matches")
    
    # Create messages
    matches = list(Match.objects.all())
    if not matches:
        print("❌ No matches found to create messages")
        return
    
    sample_messages = [
        "Hey! How's your day going?",
        "I love your profile! Tell me more about yourself.",
        "That photo is amazing! 📸",
        "Are you free for coffee this weekend?",
        "Hope you're having a great week!",
        "You seem like such an interesting person!",
        "What's your favorite spot in the city?",
        "Your bio made me smile 😊",
        "Would you like to grab dinner sometime?",
        "I love your sense of humor!"
    ]
    
    messages_created = 0
    for i in range(min(50, len(matches) * 3)):  # Create up to 50 messages
        try:
            match = random.choice(matches)
            sender = random.choice([match.user1, match.user2])
            receiver = match.user2 if sender == match.user1 else match.user1
            
            Message.objects.create(
                match=match,
                sender=sender,
                receiver=receiver,
                content=random.choice(sample_messages),
                is_read=random.choice([True, False])
            )
            messages_created += 1
            
        except Exception as e:
            print(f"   Error creating message: {e}")
            continue
    
    print(f"✅ Created {messages_created} messages")
    
    # Final stats
    print(f"\n📊 Final Database Statistics:")
    print(f"   Users: {User.objects.count()}")
    print(f"   Profiles: {Profile.objects.count()}")
    print(f"   Matches: {Match.objects.count()}")
    print(f"   Messages: {Message.objects.count()}")

def create_few_more_users():
    """Create a few more South African users"""
    print("👥 Creating a few more South African users...")
    
    sa_names = [
        ('Thabo', 'Mthembu', 'male'),
        ('Nomsa', 'Dlamini', 'female'),
        ('Sipho', 'Khumalo', 'male'),
        ('Zanele', 'Ngcobo', 'female'),
        ('Mandla', 'Zulu', 'male')
    ]
    
    sa_cities = [
        'Cape Town, Western Cape',
        'Johannesburg, Gauteng', 
        'Durban, KwaZulu-Natal',
        'Pretoria, Gauteng',
        'Port Elizabeth, Eastern Cape'
    ]
    
    bios = [
        "Love exploring the beautiful landscapes of South Africa. Always up for a good braai!",
        "Passionate about life, love, and laughter. Looking for someone to share adventures with.",
        "Fitness enthusiast who enjoys hiking Table Mountain on weekends.",
        "Foodie who loves trying new restaurants around the city.",
        "Music lover and festival goer. If you can make me laugh, you're winning!"
    ]
    
    users_created = 0
    for first_name, surname, gender in sa_names:
        try:
            email = f"{first_name.lower()}.{surname.lower()}@heartgrid.co.za"
            
            # Skip if exists
            if User.objects.filter(email=email).exists():
                continue
            
            # Create user
            from datetime import date
            birth_date = date(
                year=1990 + random.randint(0, 10),
                month=random.randint(1, 12),
                day=random.randint(1, 28)
            )
            age = 2024 - birth_date.year

            user = User.objects.create_user(
                email=email,
                name=f"{first_name} {surname}",
                password='password123',
                date_of_birth=birth_date,
                age=age
            )
            
            # Create profile
            interested_in = 'female' if gender == 'male' else 'male'
            profile = Profile.objects.create(
                user=user,
                bio=random.choice(bios),
                gender=gender,
                interested_in=interested_in,
                location=random.choice(sa_cities),
                is_complete=True,
                is_visible=True
            )
            
            # Set interests
            interests = ['hiking', 'music', 'travel', 'fitness', 'braai']
            profile.interests = random.sample(interests, 3)
            profile.save()
            
            users_created += 1
            print(f"   Created: {user.name}")
            
        except Exception as e:
            print(f"   Error creating {first_name}: {e}")
            continue
    
    print(f"✅ Created {users_created} additional users")

if __name__ == '__main__':
    print("🚀 HeartGrid Sample Data Creation")
    print("=" * 40)
    
    # Check current status
    print(f"📊 Current Status:")
    print(f"   Users: {User.objects.count()}")
    print(f"   Profiles: {Profile.objects.count()}")
    print(f"   Matches: {Match.objects.count()}")
    print(f"   Messages: {Message.objects.count()}")
    print()
    
    # Create a few more users
    create_few_more_users()
    
    # Create matches and messages
    create_matches_and_messages()
    
    print("\n🎉 Sample data creation completed!")
    print("\n💡 The database now has realistic South African dating app data for testing.")
