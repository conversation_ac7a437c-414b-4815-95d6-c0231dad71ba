#!/usr/bin/env python3
"""
Test script for HeartGrid authentication flow
"""

import requests
import sys
from urllib.parse import urljoin

BASE_URL = 'http://localhost:8000'

def test_auth_flow():
    """Test the complete authentication flow"""
    session = requests.Session()
    
    print("🔐 Testing HeartGrid Authentication Flow")
    print("=" * 50)
    
    # Test 1: Access landing page
    print("\n1. Testing landing page access...")
    try:
        response = session.get(f'{BASE_URL}/')
        if response.status_code == 200:
            print("✅ Landing page accessible")
        else:
            print(f"❌ Landing page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Landing page error: {e}")
        return False
    
    # Test 2: Access login page
    print("\n2. Testing login page access...")
    try:
        response = session.get(f'{BASE_URL}/login/')
        if response.status_code == 200:
            print("✅ Login page accessible")
            # Extract CSRF token
            if 'csrf' in response.text.lower():
                print("✅ CSRF token found in login page")
            else:
                print("⚠️  CSRF token not found in login page")
        else:
            print(f"❌ Login page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login page error: {e}")
        return False
    
    # Test 3: Access register page
    print("\n3. Testing register page access...")
    try:
        response = session.get(f'{BASE_URL}/register/')
        if response.status_code == 200:
            print("✅ Register page accessible")
            if 'csrf' in response.text.lower():
                print("✅ CSRF token found in register page")
            else:
                print("⚠️  CSRF token not found in register page")
        else:
            print(f"❌ Register page failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Register page error: {e}")
        return False
    
    # Test 4: Test protected page redirect
    print("\n4. Testing protected page redirect...")
    try:
        response = session.get(f'{BASE_URL}/discover/', allow_redirects=False)
        if response.status_code == 302:
            print("✅ Protected page correctly redirects unauthenticated users")
            redirect_url = response.headers.get('Location', '')
            if 'login' in redirect_url.lower():
                print("✅ Redirect goes to login page")
            else:
                print(f"⚠️  Redirect goes to: {redirect_url}")
        else:
            print(f"⚠️  Protected page returned: {response.status_code}")
    except Exception as e:
        print(f"❌ Protected page test error: {e}")
        return False
    
    # Test 5: Test static files
    print("\n5. Testing static files...")
    try:
        css_response = session.get(f'{BASE_URL}/static/css/style.css')
        js_response = session.get(f'{BASE_URL}/static/js/app.js')
        
        if css_response.status_code == 200:
            print("✅ CSS file accessible")
        else:
            print(f"❌ CSS file failed: {css_response.status_code}")
        
        if js_response.status_code == 200:
            print("✅ JavaScript file accessible")
        else:
            print(f"❌ JavaScript file failed: {js_response.status_code}")
            
    except Exception as e:
        print(f"❌ Static files test error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Authentication flow test completed!")
    print("\n📋 Next Steps:")
    print("   1. Test user registration via browser")
    print("   2. Test user login via browser")
    print("   3. Test protected page access after login")
    print("   4. Test logout functionality")
    
    return True

if __name__ == '__main__':
    try:
        success = test_auth_flow()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
