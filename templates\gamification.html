{% extends "base.html" %}

{% block title %}Achievements & Rewards - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- User Stats Overview -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="level-circle mb-3">
                        <span class="level-number">{{ gamification_data.level }}</span>
                    </div>
                    <h5 class="fw-bold">Level {{ gamification_data.level }}</h5>
                    <p class="text-muted mb-3">{{ gamification_data.total_points }} Total Points</p>
                    
                    <!-- Progress to next level -->
                    <div class="progress mb-3">
                        {% set next_level_points = ((gamification_data.level) * 100) ** 2 %}
                        {% set current_level_points = ((gamification_data.level - 1) * 100) ** 2 %}
                        {% set progress = ((gamification_data.total_points - current_level_points) / (next_level_points - current_level_points)) * 100 %}
                        <div class="progress-bar bg-primary" style="width: {{ progress|round }}%"></div>
                    </div>
                    <small class="text-muted">{{ ((next_level_points - gamification_data.total_points)|round)|int }} points to next level</small>
                </div>
            </div>
            
            <!-- Current Streak -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-body text-center">
                    <i class="fas fa-fire fa-2x text-warning mb-2"></i>
                    <h6 class="fw-bold">{{ gamification_data.current_streak }} Day Streak</h6>
                    <small class="text-muted">Longest: {{ gamification_data.longest_streak }} days</small>
                </div>
            </div>
        </div>
        
        <!-- Daily Challenge -->
        <div class="col-md-8 mb-4">
            {% if daily_challenge %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>{{ daily_challenge.day }} Challenge
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="fw-bold">{{ daily_challenge.task.replace('_', ' ').title() }}</h6>
                            <p class="text-muted mb-2">Complete {{ daily_challenge.target }} actions today</p>
                            
                            <!-- Progress Bar -->
                            <div class="progress mb-2">
                                {% set progress = (daily_challenge.progress / daily_challenge.target) * 100 %}
                                <div class="progress-bar bg-success" style="width: {{ progress|round }}%"></div>
                            </div>
                            <small class="text-muted">{{ daily_challenge.progress }} / {{ daily_challenge.target }}</small>
                        </div>
                        <div class="col-md-4 text-center">
                            {% if daily_challenge.completed %}
                            <div class="challenge-completed">
                                <i class="fas fa-check-circle fa-3x text-success mb-2"></i>
                                <p class="fw-bold text-success">Completed!</p>
                            </div>
                            {% else %}
                            <div class="challenge-reward">
                                <i class="fas fa-gift fa-2x text-warning mb-2"></i>
                                <p class="small">Reward: {{ daily_challenge.reward.points }} points</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="row">
        <!-- Achievements -->
        <div class="col-md-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2"></i>Achievements ({{ gamification_data.achievements|length }})
                    </h5>
                </div>
                <div class="card-body">
                    {% if gamification_data.achievements %}
                    <div class="row g-3">
                        {% for achievement in gamification_data.achievements %}
                        <div class="col-md-6">
                            <div class="achievement-card p-3 border rounded">
                                <div class="d-flex align-items-center">
                                    <div class="achievement-icon me-3">
                                        <i class="{{ achievement.icon }} fa-2x text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">{{ achievement.name }}</h6>
                                        <p class="text-muted small mb-1">{{ achievement.description }}</p>
                                        <small class="text-success">+{{ achievement.points }} points</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No achievements yet. Start exploring to earn your first achievement!</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Rewards Inventory -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-gift me-2"></i>Rewards
                    </h5>
                </div>
                <div class="card-body">
                    <div class="reward-item d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-star text-warning me-2"></i>
                            <span>Super Likes</span>
                        </div>
                        <span class="badge bg-primary">{{ gamification_data.rewards_inventory.super_likes }}</span>
                    </div>
                    
                    <div class="reward-item d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-rocket text-info me-2"></i>
                            <span>Boost Credits</span>
                        </div>
                        <span class="badge bg-primary">{{ gamification_data.rewards_inventory.boost_credits }}</span>
                    </div>
                    
                    <div class="reward-item d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-crown text-warning me-2"></i>
                            <span>Premium Days</span>
                        </div>
                        <span class="badge bg-primary">{{ gamification_data.rewards_inventory.premium_trial_days }}</span>
                    </div>
                    
                    <div class="reward-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-highlight text-success me-2"></i>
                            <span>Profile Highlights</span>
                        </div>
                        <span class="badge bg-primary">{{ gamification_data.rewards_inventory.profile_highlight }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="stat-item d-flex justify-content-between mb-2">
                        <span class="small">Total Matches:</span>
                        <strong>{{ gamification_data.statistics.total_matches }}</strong>
                    </div>
                    <div class="stat-item d-flex justify-content-between mb-2">
                        <span class="small">Likes Sent:</span>
                        <strong>{{ gamification_data.statistics.total_likes_sent }}</strong>
                    </div>
                    <div class="stat-item d-flex justify-content-between mb-2">
                        <span class="small">Messages Sent:</span>
                        <strong>{{ gamification_data.statistics.total_messages_sent }}</strong>
                    </div>
                    <div class="stat-item d-flex justify-content-between">
                        <span class="small">Super Likes:</span>
                        <strong>{{ gamification_data.statistics.total_super_likes_sent }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Leaderboard -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-medal me-2"></i>Leaderboard
                    </h5>
                </div>
                <div class="card-body">
                    {% if leaderboard %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>User</th>
                                    <th>Level</th>
                                    <th>Points</th>
                                    <th>Achievements</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in leaderboard %}
                                <tr {% if user.user_id == session.user_id %}class="table-primary"{% endif %}>
                                    <td>
                                        {% if loop.index <= 3 %}
                                        <i class="fas fa-medal text-warning"></i>
                                        {% endif %}
                                        #{{ loop.index }}
                                    </td>
                                    <td>{{ user.name if user.user_id != session.user_id else 'You' }}</td>
                                    <td>{{ user.level }}</td>
                                    <td>{{ user.points }}</td>
                                    <td>{{ user.achievements_count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No leaderboard data available.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.level-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.level-number {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.achievement-card {
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.challenge-completed i {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.reward-item {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
}

.reward-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}