{% extends "base.html" %}

{% block title %}My Matches - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="matches-header text-center mb-5">
        <i class="fas fa-heart fa-3x text-danger mb-3 heartbeat"></i>
        <h2 class="fw-bold">My Matches</h2>
        <p class="text-muted">People who liked you back</p>
    </div>
    
    {% if matches %}
        <div class="matches-grid">
            {% for match in matches %}
                <div class="match-card">
                    <div class="match-image">
                        {% if match.photos %}
                            <img src="{{ url_for('uploaded_file', filename=match.photos[0]) }}" 
                                 alt="Match Photo" class="img-fluid">
                        {% else %}
                            <div class="no-photo">
                                <i class="fas fa-user fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <div class="match-badge">
                            <i class="fas fa-heart text-white"></i>
                        </div>
                    </div>
                    
                    <div class="match-info">
                        <h5 class="match-name mb-1">
                            {{ data_store.get_user_by_id(match.user_id).name if data_store.get_user_by_id(match.user_id) else 'User' }}
                            {% if match.age %}
                                <span class="text-muted">, {{ match.age }}</span>
                            {% endif %}
                        </h5>
                        
                        {% if match.location %}
                            <p class="match-location text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ match.location }}
                            </p>
                        {% endif %}
                        
                        {% if match.bio %}
                            <p class="match-bio text-muted small mb-3">
                                {{ match.bio[:80] }}{{ '...' if match.bio|length > 80 else '' }}
                            </p>
                        {% endif %}
                        
                        <div class="match-actions">
                            <a href="{{ url_for('chat', match_user_id=match.user_id) }}" 
                               class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-comments me-1"></i>
                                Start Chat
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state text-center py-5">
            <i class="fas fa-heart-broken fa-4x text-muted mb-4"></i>
            <h4 class="text-muted mb-3">No Matches Yet</h4>
            <p class="text-muted mb-4">
                Keep browsing profiles to find your perfect match! 
                When someone likes you back, they'll appear here.
            </p>
            <a href="{{ url_for('discover') }}" class="btn btn-primary">
                <i class="fas fa-compass me-2"></i>
                Start Discovering
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
