"""
HeartGrid Cryptocurrency Payment Utilities

This module contains utilities for handling cryptocurrency payments,
converted from the original Flask crypto_payment.py module.
"""

import requests
import hashlib
import uuid
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from datetime import timedelta


class CryptoPaymentHandler:
    """
    Handles cryptocurrency payment processing for HeartGrid subscriptions
    """
    
    # Supported chains and their configurations
    SUPPORTED_CHAINS = {
        'ethereum': {
            'name': 'Ethereum',
            'symbol': 'ETH',
            'decimals': 18,
            'explorer': 'https://etherscan.io/tx/',
            'rpc_url': 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID'
        },
        'bsc': {
            'name': 'BNB Smart Chain',
            'symbol': 'BNB',
            'decimals': 18,
            'explorer': 'https://bscscan.com/tx/',
            'rpc_url': 'https://bsc-dataseed1.binance.org/'
        },
        'solana': {
            'name': '<PERSON><PERSON>',
            'symbol': 'SOL',
            'decimals': 9,
            'explorer': 'https://explorer.solana.com/tx/',
            'rpc_url': 'https://api.mainnet-beta.solana.com'
        },
        'tron': {
            'name': 'Tron',
            'symbol': 'TRX',
            'decimals': 6,
            'explorer': 'https://tronscan.org/#/transaction/',
            'rpc_url': 'https://api.trongrid.io'
        },
        'ton': {
            'name': 'TON',
            'symbol': 'TON',
            'decimals': 9,
            'explorer': 'https://tonscan.org/tx/',
            'rpc_url': 'https://toncenter.com/api/v2/'
        }
    }
    
    # Subscription pricing in USD
    SUBSCRIPTION_PRICING = {
        'weekly': Decimal('9.99'),
        'fortnightly': Decimal('17.99'),
        'monthly': Decimal('29.99')
    }
    
    def __init__(self):
        self.api_keys = {
            'coingecko': getattr(settings, 'COINGECKO_API_KEY', None),
            'moralis': getattr(settings, 'MORALIS_API_KEY', None),
        }
    
    def generate_payment_address(self, chain: str, user_id: str) -> str:
        """
        Generate a unique payment address for the user and chain
        In production, this would integrate with wallet services
        """
        # For demo purposes, generate a deterministic address
        seed = f"{chain}_{user_id}_{timezone.now().date()}"
        hash_obj = hashlib.sha256(seed.encode())
        
        if chain == 'ethereum' or chain == 'bsc':
            # Ethereum-style address
            return f"0x{hash_obj.hexdigest()[:40]}"
        elif chain == 'solana':
            # Solana-style address (base58)
            return f"Sol{hash_obj.hexdigest()[:40]}"
        elif chain == 'tron':
            # Tron-style address
            return f"TR{hash_obj.hexdigest()[:32]}"
        elif chain == 'ton':
            # TON-style address
            return f"EQ{hash_obj.hexdigest()[:46]}"
        else:
            return f"addr_{hash_obj.hexdigest()[:40]}"
    
    def get_crypto_price(self, chain: str, token_type: str = 'native') -> Decimal:
        """
        Get current cryptocurrency price in USD
        """
        try:
            # Map chains to CoinGecko IDs
            coin_ids = {
                'ethereum': 'ethereum',
                'bsc': 'binancecoin',
                'solana': 'solana',
                'tron': 'tron',
                'ton': 'the-open-network'
            }
            
            if token_type == 'usdt':
                coin_id = 'tether'
            else:
                coin_id = coin_ids.get(chain, 'ethereum')
            
            # Use CoinGecko API for price data
            url = f"https://api.coingecko.com/api/v3/simple/price"
            params = {
                'ids': coin_id,
                'vs_currencies': 'usd'
            }
            
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                price = data.get(coin_id, {}).get('usd', 0)
                return Decimal(str(price))
            else:
                # Fallback prices if API fails
                fallback_prices = {
                    'ethereum': Decimal('2000'),
                    'binancecoin': Decimal('300'),
                    'solana': Decimal('100'),
                    'tron': Decimal('0.10'),
                    'the-open-network': Decimal('2.50'),
                    'tether': Decimal('1.00')
                }
                return fallback_prices.get(coin_id, Decimal('100'))
        
        except Exception as e:
            print(f"Error fetching crypto price: {e}")
            # Return fallback price
            return Decimal('100')
    
    def calculate_crypto_amount(self, usd_amount: Decimal, chain: str, token_type: str = 'native') -> Decimal:
        """
        Calculate the amount of cryptocurrency needed for the USD amount
        """
        crypto_price = self.get_crypto_price(chain, token_type)
        if crypto_price > 0:
            crypto_amount = usd_amount / crypto_price
            # Add 2% buffer for price fluctuations
            crypto_amount = crypto_amount * Decimal('1.02')
            return crypto_amount.quantize(Decimal('0.00000001'))
        return Decimal('0')

    def get_supported_chains(self):
        """Get list of supported blockchain chains"""
        return self.SUPPORTED_CHAINS

    def create_payment_request(self, user, plan: str, chain: str, token_type: str = 'native'):
        """
        Create a new cryptocurrency payment request
        """
        from .models import CryptoPayment
        
        if plan not in self.SUBSCRIPTION_PRICING:
            raise ValueError(f"Invalid subscription plan: {plan}")
        
        if chain not in self.SUPPORTED_CHAINS:
            raise ValueError(f"Unsupported chain: {chain}")
        
        usd_amount = self.SUBSCRIPTION_PRICING[plan]
        crypto_amount = self.calculate_crypto_amount(usd_amount, chain, token_type)
        payment_address = self.generate_payment_address(chain, str(user.id))
        
        # Create payment record
        payment = CryptoPayment.objects.create(
            user=user,
            subscription_plan=plan,
            chain=chain,
            token_type=token_type,
            payment_address=payment_address,
            amount_crypto=crypto_amount,
            amount_usd=usd_amount,
            expires_at=timezone.now() + timedelta(hours=1)  # 1 hour to complete payment
        )
        
        return payment
    
    def verify_payment(self, payment_id: str, tx_hash: str) -> bool:
        """
        Verify a cryptocurrency payment transaction
        In production, this would check the blockchain
        """
        from .models import CryptoPayment
        
        try:
            payment = CryptoPayment.objects.get(id=payment_id)
            
            # For demo purposes, accept any valid-looking transaction hash
            if len(tx_hash) >= 32 and tx_hash.startswith(('0x', 'Sol', 'TR', 'EQ')):
                payment.tx_hash = tx_hash
                payment.status = 'confirmed'
                payment.confirmed_at = timezone.now()
                payment.save()
                
                # Create or update subscription
                self._create_subscription(payment)
                return True
            
            return False
        
        except CryptoPayment.DoesNotExist:
            return False
    
    def _create_subscription(self, payment):
        """
        Create or update user subscription after successful payment
        """
        from .models import Subscription
        
        # Calculate subscription duration
        duration_map = {
            'weekly': timedelta(weeks=1),
            'fortnightly': timedelta(weeks=2),
            'monthly': timedelta(days=30)
        }
        
        duration = duration_map.get(payment.subscription_plan, timedelta(weeks=1))
        
        # Define features for each plan
        features_map = {
            'weekly': ['basic_chat', 'super_likes', 'read_receipts'],
            'fortnightly': ['basic_chat', 'super_likes', 'read_receipts', 'voice_messages'],
            'monthly': ['basic_chat', 'super_likes', 'read_receipts', 'voice_messages', 'video_calls', 'priority_matching']
        }
        
        features = features_map.get(payment.subscription_plan, ['basic_chat'])
        
        # Create or update subscription
        subscription, created = Subscription.objects.get_or_create(
            user=payment.user,
            defaults={
                'plan': payment.subscription_plan,
                'status': 'active',
                'features': features,
                'expires_at': timezone.now() + duration
            }
        )
        
        if not created:
            # Extend existing subscription
            if subscription.expires_at > timezone.now():
                subscription.expires_at += duration
            else:
                subscription.expires_at = timezone.now() + duration
            
            subscription.plan = payment.subscription_plan
            subscription.status = 'active'
            subscription.features = features
            subscription.save()
        
        return subscription
    
    def get_payment_status(self, payment_id: str) -> dict:
        """
        Get the current status of a payment
        """
        from .models import CryptoPayment
        
        try:
            payment = CryptoPayment.objects.get(id=payment_id)
            
            return {
                'status': payment.status,
                'amount_crypto': str(payment.amount_crypto),
                'amount_usd': str(payment.amount_usd),
                'payment_address': payment.payment_address,
                'chain': payment.chain,
                'token_type': payment.token_type,
                'expires_at': payment.expires_at.isoformat(),
                'tx_hash': payment.tx_hash,
                'confirmed_at': payment.confirmed_at.isoformat() if payment.confirmed_at else None
            }
        
        except CryptoPayment.DoesNotExist:
            return {'status': 'not_found'}


# Global instance
crypto_handler = CryptoPaymentHandler()
