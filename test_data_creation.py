#!/usr/bin/env python
"""
Test script to debug data creation issues
"""

import os
import django
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from heartgrid.models import User, Profile, Subscription
from django.utils import timezone

def test_simple_user_creation():
    """Test creating a single user with profile"""
    print("🧪 Testing simple user creation...")
    
    try:
        # Clean up any existing test user
        User.objects.filter(email='<EMAIL>').delete()
        
        # Create user
        user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='password123',
            date_of_birth=date(1990, 1, 1),
            age=34
        )
        print(f"✅ User created: {user.name} ({user.email})")
        
        # Create profile
        profile = Profile.objects.create(
            user=user,
            bio='Test bio',
            gender='male',
            interested_in='female',
            location='Cape Town, South Africa',
            is_complete=True,
            is_visible=True
        )
        print(f"✅ Profile created: {profile.id}")
        
        # Set interests after saving
        profile.interests = ['hiking', 'music', 'travel']
        profile.save()
        print(f"✅ Interests set: {profile.interests}")
        
        # Create subscription
        subscription = Subscription.objects.create(
            user=user,
            plan='trial',
            status='active',
            expires_at=timezone.now() + timezone.timedelta(days=3),
            features=['basic_chat']
        )
        print(f"✅ Subscription created: {subscription.plan}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_status():
    """Check current database status"""
    print("\n📊 Current database status:")
    print(f"   Users: {User.objects.count()}")
    print(f"   Profiles: {Profile.objects.count()}")
    print(f"   Subscriptions: {Subscription.objects.count()}")

if __name__ == '__main__':
    print("🚀 Testing Data Creation...")
    
    check_database_status()
    
    if test_simple_user_creation():
        print("\n✅ Simple user creation test passed!")
        check_database_status()
    else:
        print("\n❌ Simple user creation test failed!")
