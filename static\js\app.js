// HeartGrid JavaScript Functions

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize form validation
    initializeFormValidation();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize smooth scrolling
    initializeSmoothScrolling();
    
    // Initialize auto-dismiss alerts
    initializeAlerts();
    
    console.log('HeartGrid app initialized');
}

// Form Validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // Custom validation for password confirmation
    const passwordConfirm = document.getElementById('password_confirm');
    const password = document.getElementById('password');
    
    if (passwordConfirm && password) {
        passwordConfirm.addEventListener('input', function() {
            if (password.value !== passwordConfirm.value) {
                passwordConfirm.setCustomValidity('Passwords do not match');
            } else {
                passwordConfirm.setCustomValidity('');
            }
        });
    }
    
    // Custom validation for date of birth (18+ requirement)
    const dateOfBirth = document.getElementById('date_of_birth');
    if (dateOfBirth) {
        // Set max date to 18 years ago
        const today = new Date();
        const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
        const minDate = new Date(today.getFullYear() - 100, today.getMonth(), today.getDate());
        
        dateOfBirth.max = maxDate.toISOString().split('T')[0];
        dateOfBirth.min = minDate.toISOString().split('T')[0];
        
        dateOfBirth.addEventListener('input', function() {
            const selectedDate = new Date(this.value);
            const age = Math.floor((today - selectedDate) / (365.25 * 24 * 60 * 60 * 1000));
            
            if (age < 18) {
                this.setCustomValidity('You must be at least 18 years old to join HeartGrid');
            } else if (age > 100) {
                this.setCustomValidity('Please enter a valid date of birth');
            } else {
                this.setCustomValidity('');
            }
        });
    }
    
    // Enhanced password validation
    const passwordField = document.getElementById('password');
    if (passwordField && passwordField.pattern) {
        passwordField.addEventListener('input', function() {
            const value = this.value;
            const hasLower = /[a-z]/.test(value);
            const hasUpper = /[A-Z]/.test(value);
            const hasNumber = /\d/.test(value);
            const isLongEnough = value.length >= 8;
            
            if (!isLongEnough) {
                this.setCustomValidity('Password must be at least 8 characters long');
            } else if (!hasLower) {
                this.setCustomValidity('Password must contain at least one lowercase letter');
            } else if (!hasUpper) {
                this.setCustomValidity('Password must contain at least one uppercase letter');
            } else if (!hasNumber) {
                this.setCustomValidity('Password must contain at least one number');
            } else {
                this.setCustomValidity('');
            }
        });
    }
}

// Initialize Bootstrap tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Auto-dismiss alerts
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000); // Auto-dismiss after 5 seconds
    });
}

// Image upload preview
function previewImage(input, previewElement) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            previewElement.src = e.target.result;
            previewElement.style.display = 'block';
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// File size validation
function validateFileSize(input, maxSizeMB = 16) {
    if (input.files && input.files[0]) {
        const fileSize = input.files[0].size / 1024 / 1024; // Convert to MB
        
        if (fileSize > maxSizeMB) {
            alert(`File size must be less than ${maxSizeMB}MB`);
            input.value = '';
            return false;
        }
    }
    return true;
}

// Image file type validation
function validateImageFile(input) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (input.files && input.files[0]) {
        const fileType = input.files[0].type;
        
        if (!allowedTypes.includes(fileType)) {
            alert('Please select a valid image file (JPG, PNG, GIF, or WebP)');
            input.value = '';
            return false;
        }
    }
    return true;
}

// Debounce function for search inputs
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Show loading state
function showLoading(element) {
    const originalText = element.innerHTML;
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    element.disabled = true;
    element.dataset.originalText = originalText;
}

// Hide loading state
function hideLoading(element) {
    element.innerHTML = element.dataset.originalText;
    element.disabled = false;
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast_' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Format timestamp for display
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
        return 'Just now';
    } else if (diffInHours < 24) {
        return Math.floor(diffInHours) + 'h ago';
    } else if (diffInHours < 168) { // 7 days
        return Math.floor(diffInHours / 24) + 'd ago';
    } else {
        return date.toLocaleDateString();
    }
}

// Handle photo upload inputs
document.addEventListener('change', function(e) {
    if (e.target.type === 'file' && e.target.accept && e.target.accept.includes('image')) {
        if (validateImageFile(e.target) && validateFileSize(e.target)) {
            // Optional: Show preview if preview element exists
            const previewId = e.target.dataset.preview;
            if (previewId) {
                const previewElement = document.getElementById(previewId);
                if (previewElement) {
                    previewImage(e.target, previewElement);
                }
            }
        }
    }
});

// Handle form submissions with loading states
document.addEventListener('submit', function(e) {
    const submitButton = e.target.querySelector('button[type="submit"]');
    if (submitButton && !submitButton.classList.contains('no-loading')) {
        showLoading(submitButton);
        
        // Restore button state if form validation fails
        setTimeout(function() {
            if (e.target.classList.contains('was-validated') && !e.target.checkValidity()) {
                hideLoading(submitButton);
            }
        }, 100);
    }
});

// Utility function to get CSRF token (if needed in future)
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : null;
}

// Handle AJAX errors globally
function handleAjaxError(error, fallbackMessage = 'An error occurred') {
    console.error('AJAX Error:', error);
    
    let message = fallbackMessage;
    if (error.responseJSON && error.responseJSON.error) {
        message = error.responseJSON.error;
    } else if (error.responseText) {
        try {
            const parsed = JSON.parse(error.responseText);
            message = parsed.error || message;
        } catch (e) {
            // Use fallback message
        }
    }
    
    showToast(message, 'error');
}

// Heart animation for buttons
function animateHeart(element) {
    element.classList.add('heartbeat');
    setTimeout(function() {
        element.classList.remove('heartbeat');
    }, 1000);
}

// Initialize heart animations on click
document.addEventListener('click', function(e) {
    if (e.target.closest('.like-btn, .btn-primary')) {
        const heartIcon = e.target.querySelector('.fa-heart');
        if (heartIcon) {
            animateHeart(heartIcon);
        }
    }
});

// Intersection Observer for animations (optional enhancement)
function initializeScrollAnimations() {
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1
    });
    
    // Observe elements with animation classes
    const animatedElements = document.querySelectorAll('.feature-card, .story-card, .profile-card, .match-card');
    animatedElements.forEach(function(el) {
        observer.observe(el);
    });
}

// Initialize scroll animations if supported
if ('IntersectionObserver' in window) {
    initializeScrollAnimations();
}

// Handle back button in chat
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/matches';
    }
}

// Export functions for global use
window.HeartGrid = {
    showToast,
    showLoading,
    hideLoading,
    formatTimestamp,
    animateHeart,
    handleAjaxError,
    previewImage,
    validateFileSize,
    validateImageFile,
    goBack
};
