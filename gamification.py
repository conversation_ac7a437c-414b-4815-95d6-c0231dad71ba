"""
Gamification System for HeartGrid
Achievements, streaks, daily challenges, and user engagement rewards
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

class GamificationEngine:
    """Manages user achievements, streaks, and engagement rewards"""
    
    def __init__(self, data_store):
        self.data_store = data_store
        
        # Achievement definitions
        self.achievements = {
            'first_match': {
                'name': 'First Match',
                'description': 'Get your first match',
                'icon': 'fas fa-heart',
                'points': 100,
                'reward': {'super_likes': 1}
            },
            'conversation_starter': {
                'name': 'Conversation Starter',
                'description': 'Send your first message',
                'icon': 'fas fa-comments',
                'points': 50,
                'reward': {'boost_credits': 1}
            },
            'profile_complete': {
                'name': 'Profile Perfectionist',
                'description': 'Complete your profile with bio, photos, and interests',
                'icon': 'fas fa-user-check',
                'points': 150,
                'reward': {'super_likes': 2}
            },
            'early_bird': {
                'name': 'Early Bird',
                'description': 'Log in before 9 AM for 3 consecutive days',
                'icon': 'fas fa-sun',
                'points': 75,
                'reward': {'boost_credits': 1}
            },
            'night_owl': {
                'name': 'Night Owl',
                'description': 'Stay active after 11 PM for 3 consecutive days',
                'icon': 'fas fa-moon',
                'points': 75,
                'reward': {'boost_credits': 1}
            },
            'social_butterfly': {
                'name': 'Social Butterfly',
                'description': 'Chat with 5 different matches',
                'icon': 'fas fa-users',
                'points': 200,
                'reward': {'premium_trial_days': 1}
            },
            'popular': {
                'name': 'Popular',
                'description': 'Receive 10 likes in a single day',
                'icon': 'fas fa-star',
                'points': 300,
                'reward': {'profile_highlight': 3}
            },
            'consistent': {
                'name': 'Consistent',
                'description': 'Log in for 7 consecutive days',
                'icon': 'fas fa-calendar-check',
                'points': 250,
                'reward': {'super_likes': 5}
            },
            'matcher': {
                'name': 'Master Matcher',
                'description': 'Get 10 total matches',
                'icon': 'fas fa-trophy',
                'points': 500,
                'reward': {'premium_trial_days': 3}
            },
            'photographer': {
                'name': 'Photographer',
                'description': 'Upload 5 photos to your profile',
                'icon': 'fas fa-camera',
                'points': 100,
                'reward': {'boost_credits': 2}
            },
            'super_liker': {
                'name': 'Super Liker',
                'description': 'Send 10 super likes',
                'icon': 'fas fa-bolt',
                'points': 150,
                'reward': {'super_likes': 3}
            },
            'premium_explorer': {
                'name': 'Premium Explorer',
                'description': 'Try premium features for the first time',
                'icon': 'fas fa-crown',
                'points': 200,
                'reward': {'premium_trial_days': 1}
            }
        }
        
        # Daily challenges
        self.daily_challenges = {
            'monday': {'task': 'like_profiles', 'target': 10, 'reward': {'points': 50, 'super_likes': 1}},
            'tuesday': {'task': 'send_messages', 'target': 5, 'reward': {'points': 75, 'boost_credits': 1}},
            'wednesday': {'task': 'update_profile', 'target': 1, 'reward': {'points': 100, 'super_likes': 2}},
            'thursday': {'task': 'complete_profile_views', 'target': 15, 'reward': {'points': 60, 'boost_credits': 1}},
            'friday': {'task': 'send_super_likes', 'target': 3, 'reward': {'points': 120, 'super_likes': 2}},
            'saturday': {'task': 'chat_sessions', 'target': 3, 'reward': {'points': 150, 'boost_credits': 2}},
            'sunday': {'task': 'profile_visits', 'target': 20, 'reward': {'points': 80, 'super_likes': 1}}
        }
        
        # Initialize user gamification data if not exists
        if not hasattr(data_store, 'user_gamification'):
            data_store.user_gamification = {}
    
    def get_user_gamification_data(self, user_id: str) -> Dict:
        """Get user's gamification data"""
        if user_id not in self.data_store.user_gamification:
            self.data_store.user_gamification[user_id] = {
                'level': 1,
                'total_points': 0,
                'achievements': [],
                'current_streak': 0,
                'longest_streak': 0,
                'last_login': None,
                'daily_challenges': {},
                'rewards_inventory': {
                    'super_likes': 3,  # Starting bonus
                    'boost_credits': 1,
                    'premium_trial_days': 0,
                    'profile_highlight': 0
                },
                'statistics': {
                    'total_likes_sent': 0,
                    'total_matches': 0,
                    'total_messages_sent': 0,
                    'total_super_likes_sent': 0,
                    'total_profile_views': 0,
                    'total_logins': 0
                }
            }
        
        return self.data_store.user_gamification[user_id]
    
    def track_user_action(self, user_id: str, action: str, **kwargs):
        """Track user action and update gamification data"""
        gamification_data = self.get_user_gamification_data(user_id)
        
        # Update statistics
        stat_mapping = {
            'like_sent': 'total_likes_sent',
            'match_created': 'total_matches',
            'message_sent': 'total_messages_sent',
            'super_like_sent': 'total_super_likes_sent',
            'profile_viewed': 'total_profile_views',
            'login': 'total_logins'
        }
        
        if action in stat_mapping:
            gamification_data['statistics'][stat_mapping[action]] += 1
        
        # Check for achievements
        self._check_achievements(user_id, action, **kwargs)
        
        # Update daily challenges
        self._update_daily_challenges(user_id, action)
        
        # Calculate level and points
        self._update_level(user_id)
    
    def track_login(self, user_id: str):
        """Track user login for streaks and daily rewards"""
        gamification_data = self.get_user_gamification_data(user_id)
        today = datetime.now().date()
        last_login = gamification_data.get('last_login')
        
        if last_login:
            last_login_date = datetime.fromisoformat(last_login).date()
            
            if last_login_date == today:
                return  # Already logged in today
            
            if last_login_date == today - timedelta(days=1):
                # Consecutive day
                gamification_data['current_streak'] += 1
            else:
                # Streak broken
                gamification_data['current_streak'] = 1
        else:
            # First login
            gamification_data['current_streak'] = 1
        
        # Update longest streak
        if gamification_data['current_streak'] > gamification_data['longest_streak']:
            gamification_data['longest_streak'] = gamification_data['current_streak']
        
        gamification_data['last_login'] = datetime.now().isoformat()
        gamification_data['statistics']['total_logins'] += 1
        
        # Award daily login rewards
        self._award_daily_login_reward(user_id)
        
        # Track login action for achievements
        self.track_user_action(user_id, 'login')
    
    def _check_achievements(self, user_id: str, action: str, **kwargs):
        """Check and award achievements"""
        gamification_data = self.get_user_gamification_data(user_id)
        stats = gamification_data['statistics']
        
        achievements_to_check = []
        
        # Action-based achievement checks
        if action == 'match_created' and stats['total_matches'] == 1:
            achievements_to_check.append('first_match')
        elif action == 'match_created' and stats['total_matches'] == 10:
            achievements_to_check.append('matcher')
        elif action == 'message_sent' and stats['total_messages_sent'] == 1:
            achievements_to_check.append('conversation_starter')
        elif action == 'super_like_sent' and stats['total_super_likes_sent'] == 10:
            achievements_to_check.append('super_liker')
        elif action == 'profile_updated':
            if self._is_profile_complete(user_id):
                achievements_to_check.append('profile_complete')
        elif action == 'photo_uploaded':
            profile = self.data_store.get_profile(user_id)
            if len(profile.get('photos', [])) >= 5:
                achievements_to_check.append('photographer')
        elif action == 'login':
            if gamification_data['current_streak'] == 7:
                achievements_to_check.append('consistent')
        
        # Award new achievements
        for achievement_id in achievements_to_check:
            if achievement_id not in gamification_data['achievements']:
                self._award_achievement(user_id, achievement_id)
    
    def _is_profile_complete(self, user_id: str) -> bool:
        """Check if user profile is complete"""
        profile = self.data_store.get_profile(user_id)
        required_fields = ['bio', 'interests', 'photos']
        
        return all(
            profile.get(field) and 
            (len(profile[field]) > 0 if isinstance(profile[field], list) else bool(profile[field]))
            for field in required_fields
        )
    
    def _award_achievement(self, user_id: str, achievement_id: str):
        """Award achievement to user"""
        gamification_data = self.get_user_gamification_data(user_id)
        achievement = self.achievements[achievement_id]
        
        # Add achievement
        gamification_data['achievements'].append({
            'id': achievement_id,
            'name': achievement['name'],
            'description': achievement['description'],
            'icon': achievement['icon'],
            'earned_at': datetime.now().isoformat(),
            'points': achievement['points']
        })
        
        # Award points
        gamification_data['total_points'] += achievement['points']
        
        # Award rewards
        reward = achievement.get('reward', {})
        for reward_type, amount in reward.items():
            if reward_type in gamification_data['rewards_inventory']:
                gamification_data['rewards_inventory'][reward_type] += amount
    
    def _update_daily_challenges(self, user_id: str, action: str):
        """Update daily challenge progress"""
        today = datetime.now().strftime('%Y-%m-%d')
        day_name = datetime.now().strftime('%A').lower()
        
        gamification_data = self.get_user_gamification_data(user_id)
        
        if today not in gamification_data['daily_challenges']:
            gamification_data['daily_challenges'][today] = {}
        
        daily_data = gamification_data['daily_challenges'][today]
        
        if day_name in self.daily_challenges:
            challenge = self.daily_challenges[day_name]
            challenge_task = challenge['task']
            
            # Map actions to challenge tasks
            action_mapping = {
                'like_sent': 'like_profiles',
                'message_sent': 'send_messages',
                'profile_updated': 'update_profile',
                'profile_viewed': 'complete_profile_views',
                'super_like_sent': 'send_super_likes',
                'chat_started': 'chat_sessions',
                'profile_visited': 'profile_visits'
            }
            
            if action in action_mapping and action_mapping[action] == challenge_task:
                if challenge_task not in daily_data:
                    daily_data[challenge_task] = 0
                
                daily_data[challenge_task] += 1
                
                # Check if challenge completed
                if daily_data[challenge_task] >= challenge['target'] and not daily_data.get('completed'):
                    daily_data['completed'] = True
                    self._award_daily_challenge_reward(user_id, challenge['reward'])
    
    def _award_daily_challenge_reward(self, user_id: str, reward: Dict):
        """Award daily challenge completion reward"""
        gamification_data = self.get_user_gamification_data(user_id)
        
        # Award points
        if 'points' in reward:
            gamification_data['total_points'] += reward['points']
        
        # Award inventory items
        for reward_type, amount in reward.items():
            if reward_type != 'points' and reward_type in gamification_data['rewards_inventory']:
                gamification_data['rewards_inventory'][reward_type] += amount
    
    def _award_daily_login_reward(self, user_id: str):
        """Award daily login streak rewards"""
        gamification_data = self.get_user_gamification_data(user_id)
        streak = gamification_data['current_streak']
        
        # Streak-based rewards
        if streak == 3:
            gamification_data['rewards_inventory']['super_likes'] += 1
        elif streak == 7:
            gamification_data['rewards_inventory']['boost_credits'] += 1
        elif streak == 14:
            gamification_data['rewards_inventory']['super_likes'] += 3
        elif streak == 30:
            gamification_data['rewards_inventory']['premium_trial_days'] += 1
    
    def _update_level(self, user_id: str):
        """Update user level based on points"""
        gamification_data = self.get_user_gamification_data(user_id)
        points = gamification_data['total_points']
        
        # Level calculation (exponential)
        new_level = min(int((points / 100) ** 0.5) + 1, 50)  # Max level 50
        gamification_data['level'] = new_level
    
    def get_daily_challenge(self, user_id: str) -> Optional[Dict]:
        """Get today's daily challenge for user"""
        today = datetime.now().strftime('%Y-%m-%d')
        day_name = datetime.now().strftime('%A').lower()
        
        if day_name not in self.daily_challenges:
            return None
        
        challenge = self.daily_challenges[day_name].copy()
        gamification_data = self.get_user_gamification_data(user_id)
        
        # Get progress
        daily_data = gamification_data['daily_challenges'].get(today, {})
        progress = daily_data.get(challenge['task'], 0)
        completed = daily_data.get('completed', False)
        
        challenge['progress'] = progress
        challenge['completed'] = completed
        challenge['day'] = day_name.title()
        
        return challenge
    
    def get_leaderboard(self, limit: int = 10) -> List[Dict]:
        """Get points-based leaderboard"""
        leaderboard = []
        
        for user_id, gamification_data in self.data_store.user_gamification.items():
            user = self.data_store.get_user_by_id(user_id)
            if user:
                leaderboard.append({
                    'user_id': user_id,
                    'name': user.get('name', 'Anonymous'),
                    'level': gamification_data['level'],
                    'points': gamification_data['total_points'],
                    'achievements_count': len(gamification_data['achievements'])
                })
        
        leaderboard.sort(key=lambda x: x['points'], reverse=True)
        return leaderboard[:limit]
    
    def use_reward(self, user_id: str, reward_type: str, amount: int = 1) -> bool:
        """Use reward item from user's inventory"""
        gamification_data = self.get_user_gamification_data(user_id)
        
        if reward_type in gamification_data['rewards_inventory']:
            if gamification_data['rewards_inventory'][reward_type] >= amount:
                gamification_data['rewards_inventory'][reward_type] -= amount
                return True
        
        return False

# Global gamification engine
gamification_engine = None

def init_gamification(data_store):
    global gamification_engine
    gamification_engine = GamificationEngine(data_store)
    return gamification_engine