#!/usr/bin/env python3
"""
Quick Django Test - Verify basic functionality
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
import json

User = get_user_model()

def main():
    print("=" * 50)
    print("HeartGrid Quick Test")
    print("=" * 50)
    
    # Test 1: Database connection
    try:
        user_count = User.objects.count()
        print(f"✓ Database connection: {user_count} users")
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False
    
    # Test 2: Django test client
    try:
        client = Client()
        
        # Test a simple endpoint
        response = client.get('/')
        print(f"✓ Django server responding: Status {response.status_code}")
        
        # Test API endpoint
        response = client.get('/api/v1/')
        print(f"✓ API endpoint responding: Status {response.status_code}")
        
    except Exception as e:
        print(f"✗ Client test error: {e}")
        return False
    
    # Test 3: User creation
    try:
        # Clean up
        User.objects.filter(email='<EMAIL>').delete()
        
        user = User.objects.create_user(
            email='<EMAIL>',
            name='Quick Test User',
            password='testpass123'
        )
        print(f"✓ User creation: {user.name}")
        
        # Test profile creation
        from heartgrid.models import Profile
        profile = Profile.objects.create(
            user=user,
            bio='Test bio',
            location='Test City'
        )
        print(f"✓ Profile creation: {profile.id}")
        
    except Exception as e:
        print(f"✗ Model creation error: {e}")
        return False
    
    print("\n🎉 All quick tests passed!")
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
