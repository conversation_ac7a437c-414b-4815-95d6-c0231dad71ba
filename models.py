import os
import uuid
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash

# In-memory storage for <PERSON> (will migrate to PostgreSQL later)
class DataStore:
    def __init__(self):
        self.users = {}
        self.profiles = {}
        self.likes = {}  # user_id -> set of liked_user_ids
        self.matches = {}  # user_id -> set of matched_user_ids
        self.messages = {}  # match_id -> list of messages
        self.subscriptions = {}  # user_id -> subscription info
        self.admins = set()  # admin user IDs
        
    def get_user_by_email(self, email):
        for user_id, user in self.users.items():
            if user['email'] == email:
                return user_id, user
        return None, None
    
    def get_user_by_id(self, user_id):
        return self.users.get(user_id)
    
    def create_user(self, email, password, name, date_of_birth=None):
        user_id = str(uuid.uuid4())
        password_hash = generate_password_hash(password)
        
        # Calculate age from date of birth
        age = None
        if date_of_birth:
            today = datetime.now().date()
            age = today.year - date_of_birth.year - ((today.month, today.day) < (date_of_birth.month, date_of_birth.day))
        
        user = {
            'id': user_id,
            'email': email,
            'password_hash': password_hash,
            'name': name,
            'date_of_birth': date_of_birth,
            'age': age,
            'created_at': datetime.now(),
            'is_active': True
        }
        
        self.users[user_id] = user
        
        # Initialize empty profile
        self.profiles[user_id] = {
            'user_id': user_id,
            'bio': '',
            'age': age,
            'gender': '',
            'interested_in': '',
            'photos': [],
            'location': '',
            'interests': [],
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        # Initialize empty likes and matches
        self.likes[user_id] = set()
        self.matches[user_id] = set()
        
        # Initialize 3-day trial subscription
        trial_end = datetime.now() + timedelta(days=3)
        self.subscriptions[user_id] = {
            'plan': 'trial',
            'status': 'active',
            'expires_at': trial_end,
            'features': ['basic_chat'],
            'created_at': datetime.now()
        }
        
        return user_id, user
    
    def verify_password(self, user, password):
        return check_password_hash(user['password_hash'], password)
    
    def update_profile(self, user_id, profile_data):
        if user_id in self.profiles:
            self.profiles[user_id].update(profile_data)
            self.profiles[user_id]['updated_at'] = datetime.now()
            return True
        return False
    
    def get_profile(self, user_id):
        return self.profiles.get(user_id)
    
    def get_discoverable_profiles(self, current_user_id, limit=20):
        """Get profiles for discovery, excluding current user and already liked users"""
        current_profile = self.profiles.get(current_user_id, {})
        current_likes = self.likes.get(current_user_id, set())
        interested_in = current_profile.get('interested_in', '')
        
        profiles = []
        for user_id, profile in self.profiles.items():
            if (user_id != current_user_id and 
                user_id not in current_likes and
                # Show all active profiles (photos optional for demo)
                self.users.get(user_id, {}).get('is_active', False)):
                
                # Basic gender filtering if preferences are set
                if interested_in and profile.get('gender'):
                    if interested_in != 'everyone' and profile['gender'] != interested_in:
                        continue
                
                profiles.append(profile)
        
        return profiles[:limit]
    
    def like_user(self, liker_id, liked_id):
        """Like a user and check for mutual match"""
        if liker_id not in self.likes:
            self.likes[liker_id] = set()
        
        self.likes[liker_id].add(liked_id)
        
        # Check if it's a mutual like (match)
        liked_user_likes = self.likes.get(liked_id, set())
        if liker_id in liked_user_likes:
            # It's a match!
            if liker_id not in self.matches:
                self.matches[liker_id] = set()
            if liked_id not in self.matches:
                self.matches[liked_id] = set()
            
            self.matches[liker_id].add(liked_id)
            self.matches[liked_id].add(liker_id)
            
            # Create match conversation
            match_id = f"{min(liker_id, liked_id)}_{max(liker_id, liked_id)}"
            if match_id not in self.messages:
                self.messages[match_id] = []
            
            return True, match_id
        
        return False, None
    
    def get_matches(self, user_id):
        """Get all matches for a user with profile info"""
        matches = []
        user_matches = self.matches.get(user_id, set())
        
        for match_id in user_matches:
            match_profile = self.profiles.get(match_id)
            if match_profile:
                matches.append(match_profile)
        
        return matches
    
    def send_message(self, sender_id, receiver_id, message_text):
        """Send a message between matched users"""
        # Verify they are matched
        if receiver_id not in self.matches.get(sender_id, set()):
            return False
        
        match_id = f"{min(sender_id, receiver_id)}_{max(sender_id, receiver_id)}"
        
        if match_id not in self.messages:
            self.messages[match_id] = []
        
        message = {
            'id': str(uuid.uuid4()),
            'sender_id': sender_id,
            'receiver_id': receiver_id,
            'message': message_text,
            'timestamp': datetime.now(),
            'read': False
        }
        
        self.messages[match_id].append(message)
        return True
    
    def get_conversation(self, user1_id, user2_id):
        """Get conversation between two matched users"""
        match_id = f"{min(user1_id, user2_id)}_{max(user1_id, user2_id)}"
        return self.messages.get(match_id, [])
    
    def get_subscription(self, user_id):
        """Get user's subscription info"""
        return self.subscriptions.get(user_id)
    
    def is_subscription_active(self, user_id):
        """Check if user has active subscription"""
        subscription = self.subscriptions.get(user_id)
        if not subscription:
            return False
        
        if subscription['status'] != 'active':
            return False
            
        # Check if subscription has expired
        if subscription['expires_at'] < datetime.now():
            subscription['status'] = 'expired'
            return False
            
        return True
    
    def can_chat(self, user_id):
        """Check if user can access chat features"""
        return self.is_subscription_active(user_id)
    
    def has_premium_features(self, user_id):
        """Check if user has voice/video call access"""
        subscription = self.subscriptions.get(user_id)
        if not subscription or not self.is_subscription_active(user_id):
            return False
        return subscription['plan'] in ['weekly', 'fortnightly', 'monthly']
    
    def create_subscription(self, user_id, plan_type):
        """Create or update subscription"""
        now = datetime.now()
        
        # Define plan durations and features
        plans = {
            'weekly': {
                'duration': timedelta(days=7),
                'features': ['basic_chat', 'voice_calls', 'video_calls', 'premium_filters']
            },
            'fortnightly': {
                'duration': timedelta(days=14),
                'features': ['basic_chat', 'voice_calls', 'video_calls', 'premium_filters']
            },
            'monthly': {
                'duration': timedelta(days=30),
                'features': ['basic_chat', 'voice_calls', 'video_calls', 'premium_filters']
            }
        }
        
        if plan_type not in plans:
            return False
            
        plan = plans[plan_type]
        expires_at = now + plan['duration']
        
        self.subscriptions[user_id] = {
            'plan': plan_type,
            'status': 'active',
            'expires_at': expires_at,
            'features': plan['features'],
            'created_at': now,
            'updated_at': now
        }
        
        return True
    
    def is_admin(self, user_id):
        """Check if user is an admin"""
        return user_id in self.admins
    
    def make_admin(self, user_id):
        """Make user an admin"""
        self.admins.add(user_id)
    
    def get_all_users_stats(self):
        """Get statistics for admin dashboard"""
        total_users = len(self.users)
        active_users = len([u for u in self.users.values() if u.get('is_active')])
        total_matches = sum(len(matches) for matches in self.matches.values()) // 2
        active_subscriptions = len([s for s in self.subscriptions.values() 
                                  if s.get('status') == 'active' and s.get('expires_at', datetime.min) > datetime.now()])
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'total_matches': total_matches,
            'active_subscriptions': active_subscriptions,
            'subscription_revenue': active_subscriptions * 10  # Mock revenue calculation
        }

# Global data store instance
data_store = DataStore()

# Sample data for demonstration and testing
def init_sample_data():
    """Initialize with sample profiles from South Africa for testing"""
    from datetime import date
    
    # Create sample South African profiles for testing
    sample_users = [
        {
            'name': 'Thabo Mthembu',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1995, 3, 15),
            'gender': 'male',
            'interested_in': 'female',
            'location': 'Cape Town, Western Cape',
            'bio': 'Adventure enthusiast from Cape Town. Love hiking Table Mountain and exploring the winelands. Looking for someone to share new experiences with.',
            'interests': ['Hiking', 'Travel', 'Photography', 'Music']
        },
        {
            'name': 'Amahle Ndlovu',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1992, 8, 22),
            'gender': 'female',
            'interested_in': 'male',
            'location': 'Johannesburg, Gauteng',
            'bio': 'Marketing professional who loves art, good food, and weekend getaways. Passionate about South African culture and travel.',
            'interests': ['Art', 'Food', 'Travel', 'Fashion']
        },
        {
            'name': 'Liam van der Merwe',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1989, 11, 5),
            'gender': 'male',
            'interested_in': 'female',
            'location': 'Durban, KwaZulu-Natal',
            'bio': 'Surfer and beach lover from Durban. Software developer by day, wave rider by weekend. Looking for someone who loves the ocean as much as I do.',
            'interests': ['Sports', 'Technology', 'Nature', 'Fitness']
        },
        {
            'name': 'Zara Patel',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1996, 6, 18),
            'gender': 'female',
            'interested_in': 'everyone',
            'location': 'Pretoria, Gauteng',
            'bio': 'Doctor with a passion for helping others. Love reading, yoga, and trying new restaurants. Seeking genuine connections and meaningful conversations.',
            'interests': ['Reading', 'Yoga', 'Food', 'Animals']
        },
        {
            'name': 'Sipho Khumalo',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1993, 1, 30),
            'gender': 'male',
            'interested_in': 'female',
            'location': 'Port Elizabeth, Eastern Cape',
            'bio': 'Teacher and musician who believes in making a difference. Play guitar in a local band and love exploring the beautiful Eastern Cape coastline.',
            'interests': ['Music', 'Nature', 'Reading', 'Sports']
        },
        {
            'name': 'Naledi Mokoena',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1991, 4, 12),
            'gender': 'female',
            'interested_in': 'male',
            'location': 'Bloemfontein, Free State',
            'bio': 'Veterinarian who adores animals and nature. Spend weekends volunteering at animal shelters and exploring national parks. Looking for a kind-hearted partner.',
            'interests': ['Animals', 'Nature', 'Photography', 'Travel']
        },
        {
            'name': 'Keagan Williams',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1994, 9, 8),
            'gender': 'male',
            'interested_in': 'female',
            'location': 'Stellenbosch, Western Cape',
            'bio': 'Wine enthusiast and chef who loves exploring South African cuisine. Looking for someone to share culinary adventures and wine tastings with.',
            'interests': ['Cooking', 'Food', 'Travel', 'Art']
        },
        {
            'name': 'Mbali Dlamini',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1997, 2, 14),
            'gender': 'female',
            'interested_in': 'male',
            'location': 'Nelspruit, Mpumalanga',
            'bio': 'Environmental scientist passionate about conservation. Love exploring the Kruger area and working on sustainability projects. Seeking an eco-conscious partner.',
            'interests': ['Nature', 'Travel', 'Animals', 'Fitness']
        },
        {
            'name': 'Arjun Singh',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1990, 7, 25),
            'gender': 'male',
            'interested_in': 'everyone',
            'location': 'Durban, KwaZulu-Natal',
            'bio': 'Entrepreneur and fitness enthusiast. Built my own tech startup and love staying active. Looking for someone ambitious and adventurous.',
            'interests': ['Technology', 'Fitness', 'Travel', 'Music']
        },
        {
            'name': 'Chloe Meyer',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1993, 12, 3),
            'gender': 'female',
            'interested_in': 'female',
            'location': 'Cape Town, Western Cape',
            'bio': 'Graphic designer and surfer who loves the creative scene in Cape Town. Always up for gallery openings, beach days, and discovering new coffee spots.',
            'interests': ['Art', 'Sports', 'Photography', 'Travel']
        },
        {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'Password123',
            'date_of_birth': date(1995, 5, 15),
            'gender': 'male',
            'interested_in': 'female',
            'location': 'Cape Town, Western Cape',
            'bio': 'Test user account for demo purposes.',
            'interests': ['Travel', 'Music', 'Technology']
        }
    ]
    
    # Create the sample users in the data store
    for user_data in sample_users:
        # Check if user already exists
        existing_user_id, existing_user = data_store.get_user_by_email(user_data['email'])
        if not existing_user:
            # Create user
            user_id, user = data_store.create_user(
                user_data['email'],
                user_data['password'],
                user_data['name'],
                user_data['date_of_birth']
            )
            
            # Update profile with additional info
            profile_update = {
                'gender': user_data['gender'],
                'interested_in': user_data['interested_in'],
                'location': user_data['location'],
                'bio': user_data['bio'],
                'interests': user_data['interests']
            }
            data_store.update_profile(user_id, profile_update)

# Create admin user
def create_admin():
    """Create default admin user"""
    admin_email = '<EMAIL>'
    existing_user_id, existing_user = data_store.get_user_by_email(admin_email)
    if not existing_user:
        from datetime import date
        user_id, user = data_store.create_user(
            admin_email,
            'Admin123!',
            'Admin User',
            date(1990, 1, 1)
        )
        data_store.make_admin(user_id)
        print(f"Admin user created: {admin_email} / Admin123!")

# Initialize sample data and admin
init_sample_data()
create_admin()