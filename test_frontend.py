#!/usr/bin/env python3
"""
Test script to verify all frontend pages are working
"""
import requests

BASE_URL = 'http://localhost:8000'

def test_frontend_pages():
    """Test all frontend pages"""
    pages = {
        'Landing Page': '/',
        'Login Page': '/login/',
        'Register Page': '/register/',
    }

    # These pages require authentication, so we expect redirects
    auth_required_pages = {
        'Discover Page': '/discover/',
        'Profile Page': '/profile/',
        'Matches Page': '/matches/',
        'Chat Page': '/chat/',
        'Subscription Page': '/subscription/',
        'Notifications Page': '/notifications/',
        'Gamification Page': '/gamification/',
    }
    
    print("=" * 60)
    print("HeartGrid Frontend Pages Test")
    print("=" * 60)

    # Test public pages
    print("\n📄 Testing Public Pages:")
    for page_name, url in pages.items():
        try:
            response = requests.get(f'{BASE_URL}{url}')
            status = response.status_code

            if status == 200:
                print(f"✓ {page_name}: {status} (OK)")
            elif status == 302:
                print(f"↗ {page_name}: {status} (Redirect)")
            elif status == 404:
                print(f"✗ {page_name}: {status} (Not Found)")
            else:
                print(f"? {page_name}: {status}")

        except Exception as e:
            print(f"✗ {page_name}: Error - {e}")

    # Test auth-required pages
    print("\n🔒 Testing Auth-Required Pages:")
    for page_name, url in auth_required_pages.items():
        try:
            response = requests.get(f'{BASE_URL}{url}')
            status = response.status_code

            if status == 200:
                print(f"✓ {page_name}: {status} (OK)")
            elif status == 302:
                print(f"↗ {page_name}: {status} (Redirect - Expected for auth)")
            elif status == 404:
                print(f"✗ {page_name}: {status} (Not Found)")
            else:
                print(f"? {page_name}: {status}")

        except Exception as e:
            print(f"✗ {page_name}: Error - {e}")

    print("\n" + "=" * 60)
    print("Frontend test completed!")

if __name__ == '__main__':
    test_frontend_pages()
