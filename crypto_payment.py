"""
Cryptocurrency Payment System for HeartGrid
Supports USDT and native coins on ETH, BNB Smart Chain, Solana, Tron & TON
"""

import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import requests

class CryptoPaymentProcessor:
    """Handles cryptocurrency payments across multiple chains"""
    
    def __init__(self):
        self.chains = {
            'ethereum': {
                'name': 'Ethereum',
                'symbol': 'ETH',
                'rpc_url': 'https://mainnet.infura.io/v3/',
                'usdt_contract': '******************************************',
                'explorer': 'https://etherscan.io/tx/',
                'decimals': 18
            },
            'bsc': {
                'name': 'BNB Smart Chain',
                'symbol': 'BNB',
                'rpc_url': 'https://bsc-dataseed.binance.org/',
                'usdt_contract': '******************************************',
                'explorer': 'https://bscscan.com/tx/',
                'decimals': 18
            },
            'solana': {
                'name': 'Solana',
                'symbol': 'SOL',
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'usdt_contract': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
                'explorer': 'https://solscan.io/tx/',
                'decimals': 9
            },
            'tron': {
                'name': 'Tron',
                'symbol': 'TRX',
                'rpc_url': 'https://api.trongrid.io',
                'usdt_contract': 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
                'explorer': 'https://tronscan.org/#/transaction/',
                'decimals': 6
            },
            'ton': {
                'name': 'TON',
                'symbol': 'TON',
                'rpc_url': 'https://toncenter.com/api/v2/',
                'usdt_contract': 'EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs',
                'explorer': 'https://tonscan.org/tx/',
                'decimals': 9
            }
        }
        
        self.subscription_prices = {
            'weekly': 5.00,
            'fortnightly': 9.99,
            'monthly': 15.99
        }
        
        # Store pending payments in memory (in production, use database)
        self.pending_payments = {}
        self.payment_addresses = {}
    
    def generate_payment_address(self, user_id: str, plan: str, chain: str) -> Dict:
        """Generate a unique payment address for user subscription"""
        
        # In production, you'd generate actual wallet addresses
        # For demo, creating deterministic addresses based on user data
        base_string = f"{user_id}_{plan}_{chain}_{int(time.time())}"
        address_hash = hashlib.sha256(base_string.encode()).hexdigest()
        
        # Generate chain-specific address format
        if chain == 'ethereum' or chain == 'bsc':
            address = f"0x{address_hash[:40]}"
        elif chain == 'solana':
            address = f"{address_hash[:44]}"
        elif chain == 'tron':
            address = f"T{address_hash[:33]}"
        elif chain == 'ton':
            address = f"EQ{address_hash[:46]}"
        else:
            address = address_hash[:40]
        
        payment_id = f"pay_{int(time.time())}_{user_id}"
        
        payment_data = {
            'payment_id': payment_id,
            'user_id': user_id,
            'plan': plan,
            'chain': chain,
            'address': address,
            'amount_usd': self.subscription_prices[plan],
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        self.pending_payments[payment_id] = payment_data
        return payment_data
    
    def get_crypto_price(self, symbol: str) -> float:
        """Get current crypto price in USD"""
        try:
            # Using CoinGecko API for price data
            coin_ids = {
                'ETH': 'ethereum',
                'BNB': 'binancecoin',
                'SOL': 'solana',
                'TRX': 'tron',
                'TON': 'the-open-network',
                'USDT': 'tether'
            }
            
            coin_id = coin_ids.get(symbol, symbol.lower())
            url = f"https://api.coingecko.com/api/v3/simple/price?ids={coin_id}&vs_currencies=usd"
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return data[coin_id]['usd']
            else:
                # Fallback prices if API fails
                fallback_prices = {
                    'ETH': 2000.0,
                    'BNB': 300.0,
                    'SOL': 100.0,
                    'TRX': 0.08,
                    'TON': 5.0,
                    'USDT': 1.0
                }
                return fallback_prices.get(symbol, 1.0)
        except Exception as e:
            print(f"Error fetching price for {symbol}: {e}")
            return 1.0
    
    def calculate_payment_amount(self, plan: str, chain: str, token_type: str) -> Dict:
        """Calculate required payment amount in crypto"""
        usd_amount = self.subscription_prices[plan]
        
        if token_type == 'USDT':
            # USDT is pegged to USD
            crypto_amount = usd_amount
            symbol = 'USDT'
        else:
            # Native token
            symbol = self.chains[chain]['symbol']
            crypto_price = self.get_crypto_price(symbol)
            crypto_amount = usd_amount / crypto_price if crypto_price > 0 else 0
        
        return {
            'amount': round(crypto_amount, 6),
            'symbol': symbol,
            'usd_value': usd_amount,
            'price_per_token': self.get_crypto_price(symbol) if token_type != 'USDT' else 1.0
        }
    
    def create_payment_request(self, user_id: str, plan: str, chain: str, token_type: str) -> Dict:
        """Create a new payment request"""
        payment_data = self.generate_payment_address(user_id, plan, chain)
        amount_data = self.calculate_payment_amount(plan, chain, token_type)
        
        payment_request = {
            **payment_data,
            'token_type': token_type,
            'amount': amount_data['amount'],
            'symbol': amount_data['symbol'],
            'chain_info': self.chains[chain],
            'qr_data': self.generate_qr_data(payment_data['address'], amount_data['amount'], amount_data['symbol']),
            'instructions': self.get_payment_instructions(chain, token_type)
        }
        
        return payment_request
    
    def generate_qr_data(self, address: str, amount: float, symbol: str) -> str:
        """Generate QR code data for payment"""
        # Format varies by chain, this is a simplified version
        return f"crypto:{symbol}:{address}:{amount}"
    
    def get_payment_instructions(self, chain: str, token_type: str) -> Dict:
        """Get chain-specific payment instructions"""
        chain_info = self.chains[chain]
        
        instructions = {
            'title': f'Pay with {token_type} on {chain_info["name"]}',
            'steps': [
                f'Open your {chain_info["name"]} wallet',
                f'Send exactly the specified amount of {token_type}',
                'Copy the payment address exactly',
                'Double-check the network and amount',
                'Confirm the transaction'
            ],
            'warnings': [
                f'Only send {token_type} on {chain_info["name"]} network',
                'Sending wrong token or network will result in loss',
                'Payment expires in 24 hours',
                'Contact support if you need help'
            ]
        }
        
        if token_type == 'USDT':
            instructions['contract_address'] = chain_info['usdt_contract']
            instructions['steps'].insert(2, f'Use contract address: {chain_info["usdt_contract"]}')
        
        return instructions
    
    def check_payment_status(self, payment_id: str) -> Dict:
        """Check if payment has been received (simplified version)"""
        payment = self.pending_payments.get(payment_id)
        if not payment:
            return {'status': 'not_found'}
        
        # In production, you would check the blockchain for transactions
        # For demo purposes, we'll simulate payment confirmation after some time
        created_time = datetime.fromisoformat(payment['created_at'])
        if datetime.now() - created_time > timedelta(minutes=5):
            # Simulate payment confirmation
            payment['status'] = 'confirmed'
            payment['confirmed_at'] = datetime.now().isoformat()
            payment['tx_hash'] = f"0x{hashlib.sha256(payment_id.encode()).hexdigest()}"
        
        return payment
    
    def verify_payment(self, payment_id: str, tx_hash: str) -> bool:
        """Verify payment transaction on blockchain"""
        # In production, implement actual blockchain verification
        # This is a simplified version
        payment = self.pending_payments.get(payment_id)
        if payment and tx_hash:
            payment['status'] = 'confirmed'
            payment['tx_hash'] = tx_hash
            payment['confirmed_at'] = datetime.now().isoformat()
            return True
        return False
    
    def get_supported_networks(self) -> Dict:
        """Get list of supported networks and tokens"""
        networks = []
        for chain_id, chain_info in self.chains.items():
            networks.append({
                'id': chain_id,
                'name': chain_info['name'],
                'symbol': chain_info['symbol'],
                'tokens': ['Native', 'USDT'],
                'explorer': chain_info['explorer']
            })
        return {
            'networks': networks,
            'prices': self.subscription_prices
        }

# Global instance
crypto_processor = CryptoPaymentProcessor()