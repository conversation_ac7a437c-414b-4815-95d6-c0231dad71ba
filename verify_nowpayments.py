#!/usr/bin/env python3
"""
Simple NOWPayments Integration Verification Script

Verifies that the NOWPayments button has been properly integrated
into the HeartGrid Django application templates.
"""

import os
import re

def check_subscription_template():
    """Check the subscription.html template for NOWPayments integration"""
    print("🔍 Checking subscription.html template...")
    
    template_path = "templates/subscription.html"
    
    if not os.path.exists(template_path):
        print("❌ subscription.html template not found")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("NOWPayments URL", "https://nowpayments.io/payment/?iid=5532080560&source=button"),
        ("Target blank", 'target="_blank"'),
        ("Security attributes", 'rel="noreferrer noopener"'),
        ("NOWPayments image", "https://nowpayments.io/images/embeds/payment-button-white.svg"),
        ("Button text", "Pay with Crypto via NOWPayments"),
        ("CSS class", "nowpayments-btn"),
        ("Security icon", "fas fa-shield-alt"),
    ]
    
    all_passed = True
    for check_name, check_pattern in checks:
        if check_pattern in content:
            print(f"✅ {check_name} found")
        else:
            print(f"❌ {check_name} missing")
            all_passed = False
    
    # Check for CSS styling
    if ".nowpayments-btn" in content:
        print("✅ NOWPayments CSS styling found")
    else:
        print("❌ NOWPayments CSS styling missing")
        all_passed = False
    
    return all_passed

def check_payment_options_template():
    """Check the payment_options.html template for NOWPayments integration"""
    print("\n🔍 Checking payment_options.html template...")
    
    template_path = "templates/payment_options.html"
    
    if not os.path.exists(template_path):
        print("❌ payment_options.html template not found")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("NOWPayments URL", "https://nowpayments.io/payment/?iid=5532080560&source=button"),
        ("Weekly plan condition", "{% if plan == 'weekly' %}"),
        ("Django URL pattern", "{% url 'heartgrid_frontend:subscription_page' %}"),
        ("NOWPayments CSS", ".nowpayments-btn"),
    ]
    
    all_passed = True
    for check_name, check_pattern in checks:
        if check_pattern in content:
            print(f"✅ {check_name} found")
        else:
            print(f"❌ {check_name} missing")
            all_passed = False
    
    return all_passed

def check_file_structure():
    """Check that all necessary files are in place"""
    print("\n🔍 Checking file structure...")
    
    required_files = [
        "templates/subscription.html",
        "templates/payment_options.html",
        "templates/base.html",
        "manage.py",
        "heartgrid/settings.py",
    ]
    
    all_found = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_found = False
    
    return all_found

def check_integration_completeness():
    """Check that the integration is complete and follows requirements"""
    print("\n🔍 Checking integration completeness...")
    
    # Read subscription template
    with open("templates/subscription.html", 'r', encoding='utf-8') as f:
        sub_content = f.read()
    
    # Check for proper placement in weekly section
    weekly_section_pattern = r'Weekly.*?Pay with Crypto via NOWPayments'
    if re.search(weekly_section_pattern, sub_content, re.DOTALL):
        print("✅ NOWPayments button properly placed in weekly section")
    else:
        print("❌ NOWPayments button not in weekly section")
        return False
    
    # Check for responsive design elements
    responsive_checks = [
        ("Bootstrap classes", "w-100"),
        ("Responsive spacing", "mt-2 mb-2"),
        ("Mobile-friendly styling", "@media (max-width: 768px)"),
    ]
    
    for check_name, pattern in responsive_checks:
        if pattern in sub_content:
            print(f"✅ {check_name} implemented")
        else:
            print(f"⚠️  {check_name} may need attention")
    
    return True

def main():
    """Run all verification checks"""
    print("🚀 NOWPayments Integration Verification")
    print("=" * 50)
    
    results = []
    results.append(check_file_structure())
    results.append(check_subscription_template())
    results.append(check_payment_options_template())
    results.append(check_integration_completeness())
    
    print("\n" + "=" * 50)
    
    if all(results):
        print("🎉 All checks passed! NOWPayments integration is complete.")
        print("\n✅ Integration Summary:")
        print("   • NOWPayments button added to weekly subscription plan")
        print("   • Button opens in new tab with security attributes")
        print("   • Proper DaisyUI/Tailwind CSS styling applied")
        print("   • Responsive design implemented")
        print("   • Payment options page updated")
        print("   • Django URL patterns corrected")
        
        print("\n🧪 Manual Testing Steps:")
        print("   1. Open http://localhost:8000/subscription/ in browser")
        print("   2. Locate the weekly plan section")
        print("   3. Click 'Pay with Crypto via NOWPayments' button")
        print("   4. Verify it opens NOWPayments in new tab")
        print("   5. Test responsive design on different screen sizes")
        
    else:
        print("❌ Some checks failed. Please review the issues above.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
