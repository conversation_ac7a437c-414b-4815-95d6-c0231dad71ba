#!/usr/bin/env python3
"""
Simple NOWPayments Integration Verification Script

Verifies that the NOWPayments button has been properly integrated
into the HeartGrid Django application templates.
"""

import os
import re

def check_subscription_template():
    """Check the subscription.html template for NOWPayments integration"""
    print("🔍 Checking subscription.html template...")

    template_path = "templates/subscription.html"

    if not os.path.exists(template_path):
        print("❌ subscription.html template not found")
        return False

    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Check for all three NOWPayments integrations
    payment_ids = {
        "Weekly": "iid=5532080560",
        "Fortnightly": "iid=4564770173",
        "Monthly": "iid=5119273624"
    }

    all_passed = True

    # Check each plan's NOWPayments integration
    for plan_name, payment_id in payment_ids.items():
        print(f"\n  📋 Checking {plan_name} Plan:")
        if payment_id in content:
            print(f"    ✅ {plan_name} NOWPayments payment ID found")
        else:
            print(f"    ❌ {plan_name} NOWPayments payment ID missing")
            all_passed = False

    # Check common elements
    common_checks = [
        ("Target blank", 'target="_blank"'),
        ("Security attributes", 'rel="noreferrer noopener"'),
        ("NOWPayments image", "https://nowpayments.io/images/embeds/payment-button-white.svg"),
        ("Button text", "Pay with Crypto via NOWPayments"),
        ("CSS class", "nowpayments-btn"),
        ("Security icon", "fas fa-shield-alt"),
    ]

    print(f"\n  📋 Checking Common Elements:")
    for check_name, check_pattern in common_checks:
        if check_pattern in content:
            print(f"    ✅ {check_name} found")
        else:
            print(f"    ❌ {check_name} missing")
            all_passed = False

    # Check for CSS styling
    if ".nowpayments-btn" in content:
        print("    ✅ NOWPayments CSS styling found")
    else:
        print("    ❌ NOWPayments CSS styling missing")
        all_passed = False

    # Check monthly price update
    if "$19.99" in content and "19.99" in content:
        print("    ✅ Monthly price updated to $19.99")
    else:
        print("    ❌ Monthly price not updated to $19.99")
        all_passed = False

    return all_passed

def check_payment_options_template():
    """Check the payment_options.html template for NOWPayments integration"""
    print("\n🔍 Checking payment_options.html template...")

    template_path = "templates/payment_options.html"

    if not os.path.exists(template_path):
        print("❌ payment_options.html template not found")
        return False

    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Check for all three NOWPayments integrations
    payment_ids = {
        "Weekly": "iid=5532080560",
        "Fortnightly": "iid=4564770173",
        "Monthly": "iid=5119273624"
    }

    all_passed = True

    # Check each plan's conditional NOWPayments integration
    for plan_name, payment_id in payment_ids.items():
        plan_lower = plan_name.lower()
        print(f"\n  📋 Checking {plan_name} Plan:")

        if payment_id in content:
            print(f"    ✅ {plan_name} NOWPayments payment ID found")
        else:
            print(f"    ❌ {plan_name} NOWPayments payment ID missing")
            all_passed = False

        if f"plan == '{plan_lower}'" in content:
            print(f"    ✅ {plan_name} conditional logic found")
        else:
            print(f"    ❌ {plan_name} conditional logic missing")
            all_passed = False

    # Check common elements
    common_checks = [
        ("Django URL pattern", "{% url 'heartgrid_frontend:subscription_page' %}"),
        ("NOWPayments CSS", ".nowpayments-btn"),
        ("Target blank", 'target="_blank"'),
        ("Security attributes", 'rel="noreferrer noopener"'),
    ]

    print(f"\n  📋 Checking Common Elements:")
    for check_name, check_pattern in common_checks:
        if check_pattern in content:
            print(f"    ✅ {check_name} found")
        else:
            print(f"    ❌ {check_name} missing")
            all_passed = False

    return all_passed

def check_file_structure():
    """Check that all necessary files are in place"""
    print("\n🔍 Checking file structure...")
    
    required_files = [
        "templates/subscription.html",
        "templates/payment_options.html",
        "templates/base.html",
        "manage.py",
        "heartgrid/settings.py",
    ]
    
    all_found = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_found = False
    
    return all_found

def check_integration_completeness():
    """Check that the integration is complete and follows requirements"""
    print("\n🔍 Checking integration completeness...")

    # Read subscription template
    with open("templates/subscription.html", 'r', encoding='utf-8') as f:
        sub_content = f.read()

    all_passed = True

    # Check for proper placement in all plan sections
    plan_sections = {
        "Weekly": r'Weekly.*?iid=5532080560',
        "Fortnightly": r'Fortnightly.*?iid=4564770173',
        "Monthly": r'Monthly.*?iid=5119273624'
    }

    for plan_name, pattern in plan_sections.items():
        if re.search(pattern, sub_content, re.DOTALL):
            print(f"✅ NOWPayments button properly placed in {plan_name} section")
        else:
            print(f"❌ NOWPayments button not in {plan_name} section")
            all_passed = False

    # Check for responsive design elements
    responsive_checks = [
        ("Bootstrap classes", "w-100"),
        ("Responsive spacing", "mt-2 mb-2"),
        ("Mobile-friendly styling", "@media (max-width: 768px)"),
    ]

    print(f"\n  📋 Checking Responsive Design:")
    for check_name, pattern in responsive_checks:
        if pattern in sub_content:
            print(f"    ✅ {check_name} implemented")
        else:
            print(f"    ⚠️  {check_name} may need attention")

    # Check crypto_utils pricing
    try:
        with open("heartgrid/crypto_utils.py", 'r', encoding='utf-8') as f:
            crypto_content = f.read()

        print(f"\n  📋 Checking Pricing Configuration:")
        pricing_checks = [
            ("Weekly $5.00", "Decimal('5.00')"),
            ("Fortnightly $9.99", "Decimal('9.99')"),
            ("Monthly $19.99", "Decimal('19.99')"),
        ]

        for check_name, pattern in pricing_checks:
            if pattern in crypto_content:
                print(f"    ✅ {check_name} configured correctly")
            else:
                print(f"    ❌ {check_name} pricing incorrect")
                all_passed = False

    except FileNotFoundError:
        print("    ❌ crypto_utils.py not found")
        all_passed = False

    return all_passed

def main():
    """Run all verification checks"""
    print("🚀 NOWPayments Integration Verification")
    print("=" * 50)
    
    results = []
    results.append(check_file_structure())
    results.append(check_subscription_template())
    results.append(check_payment_options_template())
    results.append(check_integration_completeness())
    
    print("\n" + "=" * 50)
    
    if all(results):
        print("🎉 All checks passed! NOWPayments integration is complete.")
        print("\n✅ Integration Summary:")
        print("   • NOWPayments buttons added to ALL subscription plans:")
        print("     - Weekly: iid=5532080560 ($5.00)")
        print("     - Fortnightly: iid=4564770173 ($9.99)")
        print("     - Monthly: iid=5119273624 ($19.99)")
        print("   • All buttons open in new tab with security attributes")
        print("   • Consistent DaisyUI/Tailwind CSS styling applied")
        print("   • Responsive design implemented across all plans")
        print("   • Payment options page updated with conditional logic")
        print("   • Monthly subscription price updated from $15.99 to $19.99")
        print("   • Crypto pricing configuration updated")

        print("\n🧪 Manual Testing Steps:")
        print("   1. Open http://localhost:8000/subscription/ in browser")
        print("   2. Verify all three plans show NOWPayments buttons:")
        print("      - Weekly plan (left): NOWPayments button present")
        print("      - Fortnightly plan (center): NOWPayments button present")
        print("      - Monthly plan (right): NOWPayments button present")
        print("   3. Click each NOWPayments button to verify correct payment IDs")
        print("   4. Test responsive design on different screen sizes")
        print("   5. Verify monthly plan shows $19.99 (not $15.99)")

    else:
        print("❌ Some checks failed. Please review the issues above.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
