#!/usr/bin/env python
"""
Simple test script to verify Django API endpoints are working
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from heartgrid.models import Profile

User = get_user_model()

def test_django_setup():
    """Test basic Django setup"""
    print("Testing Django setup...")
    
    # Test database connection
    try:
        user_count = User.objects.count()
        print(f"✓ Database connection working. Users in DB: {user_count}")
    except Exception as e:
        print(f"✗ Database error: {e}")
        return False
    
    # Test model creation
    try:
        test_user = User(
            email="<EMAIL>",
            name="Test User"
        )
        test_user.set_password("testpass123")
        print("✓ User model creation working")
    except Exception as e:
        print(f"✗ User model error: {e}")
        return False
    
    return True

def test_api_endpoints():
    """Test API endpoints using Django test client"""
    print("\nTesting API endpoints...")

    client = Client()

    # Clear existing users for clean test
    User.objects.filter(email='<EMAIL>').delete()

    # Test registration endpoint
    try:
        response = client.post('/api/v1/auth/register/', {
            'email': '<EMAIL>',
            'name': 'Test User',
            'date_of_birth': '1990-01-01',
            'password': 'testpass123',
            'password_confirm': 'testpass123'
        }, content_type='application/json')

        print(f"Registration endpoint status: {response.status_code}")
        if response.status_code in [200, 201]:
            print("✓ Registration endpoint working")
            # Get token from response
            import json
            data = json.loads(response.content)
            token = data.get('token')
        else:
            print(f"✗ Registration endpoint error: {response.content}")
            return
    except Exception as e:
        print(f"✗ Registration endpoint error: {e}")
        return

    # Test login endpoint
    try:
        response = client.post('/api/v1/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }, content_type='application/json')

        print(f"Login endpoint status: {response.status_code}")
        if response.status_code == 200:
            print("✓ Login endpoint working")
        else:
            print(f"✗ Login endpoint error: {response.content}")
    except Exception as e:
        print(f"✗ Login endpoint error: {e}")

    # Test authenticated endpoints with token
    if token:
        headers = {'HTTP_AUTHORIZATION': f'Token {token}'}

        # Test profile endpoint
        try:
            response = client.get('/api/v1/profiles/', **headers)
            print(f"Profile endpoint status: {response.status_code}")
            if response.status_code == 200:
                print("✓ Profile endpoint working")
            else:
                print(f"✗ Profile endpoint error: {response.content}")
        except Exception as e:
            print(f"✗ Profile endpoint error: {e}")

        # Test discover endpoint
        try:
            response = client.get('/api/v1/discover/', **headers)
            print(f"Discover endpoint status: {response.status_code}")
            if response.status_code == 200:
                print("✓ Discover endpoint working")
            else:
                print(f"✗ Discover endpoint error: {response.content}")
        except Exception as e:
            print(f"✗ Discover endpoint error: {e}")

        # Test subscription endpoint
        try:
            response = client.get('/api/v1/subscription/', **headers)
            print(f"Subscription endpoint status: {response.status_code}")
            if response.status_code in [200, 404]:  # 404 is ok if no subscription
                print("✓ Subscription endpoint working")
            else:
                print(f"✗ Subscription endpoint error: {response.content}")
        except Exception as e:
            print(f"✗ Subscription endpoint error: {e}")

        # Test notifications endpoint
        try:
            response = client.get('/api/v1/notifications/', **headers)
            print(f"Notifications endpoint status: {response.status_code}")
            if response.status_code == 200:
                print("✓ Notifications endpoint working")
            else:
                print(f"✗ Notifications endpoint error: {response.content}")
        except Exception as e:
            print(f"✗ Notifications endpoint error: {e}")

        # Test user stats endpoint
        try:
            response = client.get('/api/v1/stats/', **headers)
            print(f"User stats endpoint status: {response.status_code}")
            if response.status_code == 200:
                print("✓ User stats endpoint working")
            else:
                print(f"✗ User stats endpoint error: {response.content}")
        except Exception as e:
            print(f"✗ User stats endpoint error: {e}")

if __name__ == "__main__":
    print("HeartGrid Django API Test")
    print("=" * 40)
    
    if test_django_setup():
        test_api_endpoints()
    
    print("\nTest completed!")
