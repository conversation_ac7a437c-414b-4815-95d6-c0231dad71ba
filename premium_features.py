"""
Premium Features Implementation for HeartGrid
Handles all subscription-based features and restrictions
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional

class PremiumFeatures:
    """Manages all premium subscription features"""
    
    def __init__(self, data_store):
        self.data_store = data_store
        
        # Feature access matrix by plan
        self.feature_matrix = {
            'trial': {
                'unlimited_messaging': True,  # 3-day trial includes messaging
                'voice_calls': False,
                'video_calls': False,
                'premium_filters': False,
                'priority_support': False,
                'extended_match_radius': False,
                'read_receipts': False,
                'profile_boost': False,
                'advanced_analytics': False,
                'unlimited_super_likes': False,
                'profile_insights': False,
                'priority_matching': False,
                'exclusive_features': False
            },
            'weekly': {
                'unlimited_messaging': True,
                'voice_calls': True,
                'video_calls': True,
                'premium_filters': True,
                'priority_support': True,
                'extended_match_radius': False,
                'read_receipts': False,
                'profile_boost': False,
                'advanced_analytics': False,
                'unlimited_super_likes': False,
                'profile_insights': False,
                'priority_matching': False,
                'exclusive_features': False
            },
            'fortnightly': {
                'unlimited_messaging': True,
                'voice_calls': True,
                'video_calls': True,
                'premium_filters': True,
                'priority_support': True,
                'extended_match_radius': True,
                'read_receipts': True,
                'profile_boost': True,
                'advanced_analytics': True,
                'unlimited_super_likes': False,
                'profile_insights': False,
                'priority_matching': False,
                'exclusive_features': False
            },
            'monthly': {
                'unlimited_messaging': True,
                'voice_calls': True,
                'video_calls': True,
                'premium_filters': True,
                'priority_support': True,
                'extended_match_radius': True,
                'read_receipts': True,
                'profile_boost': True,
                'advanced_analytics': True,
                'unlimited_super_likes': True,
                'profile_insights': True,
                'priority_matching': True,
                'exclusive_features': True
            }
        }
        
        # User limits for non-premium features
        self.free_limits = {
            'daily_likes': 10,
            'daily_super_likes': 1,
            'match_radius_km': 25,
            'profile_views_per_day': 50
        }
        
        # Track user daily activities
        self.daily_activities = {}
    
    def has_feature(self, user_id: str, feature: str) -> bool:
        """Check if user has access to specific feature"""
        subscription = self.data_store.get_subscription(user_id)
        if not subscription:
            return False
        
        plan = subscription.get('plan', 'trial')
        
        # Check if subscription is active
        if not self.data_store.is_subscription_active(user_id):
            # Expired users lose all premium features
            return self.feature_matrix.get('trial', {}).get(feature, False)
        
        return self.feature_matrix.get(plan, {}).get(feature, False)
    
    def get_user_features(self, user_id: str) -> Dict:
        """Get all available features for user"""
        subscription = self.data_store.get_subscription(user_id)
        if not subscription:
            return self.feature_matrix['trial']
        
        plan = subscription.get('plan', 'trial')
        
        if not self.data_store.is_subscription_active(user_id):
            return self.feature_matrix['trial']
        
        return self.feature_matrix.get(plan, self.feature_matrix['trial'])
    
    def can_send_message(self, user_id: str) -> tuple[bool, str]:
        """Check if user can send messages"""
        if not self.data_store.can_chat(user_id):
            return False, "Your trial has expired. Subscribe to continue chatting!"
        
        if self.has_feature(user_id, 'unlimited_messaging'):
            return True, ""
        
        # Check daily message limit for non-premium users
        today = datetime.now().date()
        activities = self.daily_activities.get(user_id, {}).get(today, {})
        messages_sent = activities.get('messages_sent', 0)
        
        if messages_sent >= 20:  # Free users: 20 messages per day
            return False, "Daily message limit reached. Upgrade to premium for unlimited messaging!"
        
        return True, ""
    
    def can_make_call(self, user_id: str, call_type: str) -> tuple[bool, str]:
        """Check if user can make voice/video calls"""
        if call_type == 'voice' and not self.has_feature(user_id, 'voice_calls'):
            return False, "Voice calls require a premium subscription!"
        
        if call_type == 'video' and not self.has_feature(user_id, 'video_calls'):
            return False, "Video calls require a premium subscription!"
        
        return True, ""
    
    def can_use_premium_filters(self, user_id: str) -> bool:
        """Check if user can use advanced search filters"""
        return self.has_feature(user_id, 'premium_filters')
    
    def get_match_radius(self, user_id: str) -> int:
        """Get user's maximum match radius in kilometers"""
        if self.has_feature(user_id, 'extended_match_radius'):
            return 100  # Premium users: 100km radius
        return self.free_limits['match_radius_km']  # Free users: 25km radius
    
    def can_see_read_receipts(self, user_id: str) -> bool:
        """Check if user can see message read receipts"""
        return self.has_feature(user_id, 'read_receipts')
    
    def can_boost_profile(self, user_id: str) -> tuple[bool, str]:
        """Check if user can boost their profile"""
        if not self.has_feature(user_id, 'profile_boost'):
            return False, "Profile boost requires fortnightly or monthly subscription!"
        
        # Check if already boosted today
        today = datetime.now().date()
        activities = self.daily_activities.get(user_id, {}).get(today, {})
        boosts_used = activities.get('profile_boosts', 0)
        
        if boosts_used >= 3:  # Max 3 boosts per day
            return False, "You've reached your daily boost limit (3 per day)!"
        
        return True, ""
    
    def can_super_like(self, user_id: str) -> tuple[bool, str]:
        """Check if user can send super likes"""
        today = datetime.now().date()
        activities = self.daily_activities.get(user_id, {}).get(today, {})
        super_likes_used = activities.get('super_likes', 0)
        
        if self.has_feature(user_id, 'unlimited_super_likes'):
            return True, ""
        
        if super_likes_used >= self.free_limits['daily_super_likes']:
            return False, "Daily super like limit reached! Upgrade for unlimited super likes."
        
        return True, ""
    
    def can_view_profile_insights(self, user_id: str) -> bool:
        """Check if user can view detailed profile analytics"""
        return self.has_feature(user_id, 'profile_insights')
    
    def has_priority_matching(self, user_id: str) -> bool:
        """Check if user has priority in matching algorithm"""
        return self.has_feature(user_id, 'priority_matching')
    
    def get_daily_like_limit(self, user_id: str) -> int:
        """Get user's daily like limit"""
        if self.has_feature(user_id, 'unlimited_messaging'):
            return float('inf')  # Unlimited for premium users
        return self.free_limits['daily_likes']  # 10 likes for free users
    
    def track_activity(self, user_id: str, activity_type: str):
        """Track user daily activities"""
        today = datetime.now().date()
        
        if user_id not in self.daily_activities:
            self.daily_activities[user_id] = {}
        
        if today not in self.daily_activities[user_id]:
            self.daily_activities[user_id][today] = {}
        
        current_count = self.daily_activities[user_id][today].get(activity_type, 0)
        self.daily_activities[user_id][today][activity_type] = current_count + 1
    
    def get_user_analytics(self, user_id: str) -> Dict:
        """Get user profile analytics (premium feature)"""
        if not self.has_feature(user_id, 'advanced_analytics'):
            return {'error': 'Advanced analytics require fortnightly or monthly subscription!'}
        
        # Mock analytics data
        profile_views = len(self.data_store.profiles) * 2  # Simulate profile views
        likes_received = len(self.data_store.likes.get(user_id, set())) * 3
        matches_count = len(self.data_store.matches.get(user_id, set()))
        
        return {
            'profile_views': profile_views,
            'likes_received': likes_received,
            'matches_count': matches_count,
            'response_rate': min(85 + (matches_count * 2), 100),  # Simulate response rate
            'most_active_time': '8:00 PM - 10:00 PM',
            'top_age_group': '25-30',
            'weekly_trend': 'increasing'
        }
    
    def get_priority_support_status(self, user_id: str) -> Dict:
        """Get priority support information"""
        if not self.has_feature(user_id, 'priority_support'):
            return {
                'has_priority': False,
                'response_time': '24-48 hours',
                'support_level': 'Standard'
            }
        
        return {
            'has_priority': True,
            'response_time': '2-4 hours',
            'support_level': 'Priority',
            'dedicated_support': True
        }

# Global premium features instance
premium_features = None

def init_premium_features(data_store):
    global premium_features
    premium_features = PremiumFeatures(data_store)
    return premium_features