#!/usr/bin/env python
"""
HeartGrid Sample Data Population Script

This script populates the Django database with realistic South African sample data
including users, profiles, matches, messages, and subscriptions.
"""

import os
import sys
import django
import random
from datetime import datetime, timedelta, date
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from heartgrid.models import User, Profile, Match, Message, Subscription, Achievement, CryptoPayment
from heartgrid.gamification import GamificationEngine

# South African sample data
SOUTH_AFRICAN_NAMES = {
    'male': [
        'Thabo', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
    ],
    'female': [
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>in<PERSON><PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Layla', 'Amina', 'Safiya', 'Khadija'
    ]
}

SOUTH_AFRICAN_SURNAMES = [
    'Mthembu', 'Nkomo', 'Dlamini', 'Khumalo', 'Mahlangu', 'Mokoena', 'Chabalala', 'Mabaso',
    'Ndlovu', 'Zulu', 'Xaba', 'Sithole', 'Shange', 'Mnguni', 'Cele', 'Ngcobo',
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia',
    'Van der Merwe', 'Botha', 'Pretorius', 'Du Plessis', 'Van Wyk', 'Steyn', 'Fourie',
    'Patel', 'Singh', 'Kumar', 'Sharma', 'Reddy', 'Naidoo', 'Pillay', 'Maharaj'
]

SOUTH_AFRICAN_CITIES = [
    'Cape Town, Western Cape', 'Johannesburg, Gauteng', 'Durban, KwaZulu-Natal',
    'Pretoria, Gauteng', 'Port Elizabeth, Eastern Cape', 'Bloemfontein, Free State',
    'East London, Eastern Cape', 'Nelspruit, Mpumalanga', 'Kimberley, Northern Cape',
    'Polokwane, Limpopo', 'Rustenburg, North West', 'Pietermaritzburg, KwaZulu-Natal',
    'Witbank, Mpumalanga', 'Vereeniging, Gauteng', 'Welkom, Free State',
    'Klerksdorp, North West', 'Midrand, Gauteng', 'Centurion, Gauteng',
    'Stellenbosch, Western Cape', 'Paarl, Western Cape', 'George, Western Cape'
]

INTERESTS = [
    'hiking', 'braai', 'rugby', 'cricket', 'soccer', 'music', 'dancing', 'reading',
    'cooking', 'travel', 'photography', 'fitness', 'yoga', 'swimming', 'cycling',
    'movies', 'art', 'wine tasting', 'surfing', 'rock climbing', 'gaming',
    'birdwatching', 'gardening', 'volunteering', 'languages', 'technology',
    'entrepreneurship', 'fashion', 'meditation', 'running', 'tennis', 'golf'
]

BIOS = [
    "Love exploring the beautiful landscapes of South Africa. Always up for a good braai and great conversation!",
    "Passionate about life, love, and laughter. Looking for someone to share adventures with.",
    "Fitness enthusiast who enjoys hiking Table Mountain on weekends. Let's explore together!",
    "Foodie who loves trying new restaurants around Cape Town. Wine lover and sunset chaser.",
    "Music lover and festival goer. If you can make me laugh, you're already winning!",
    "Adventure seeker who loves road trips along the Garden Route. Life's too short for boring dates!",
    "Bookworm by day, social butterfly by night. Love deep conversations over good coffee.",
    "Entrepreneur with a passion for making a difference. Looking for someone who shares my values.",
    "Beach lover who enjoys long walks and watching the sunrise. Simple pleasures make me happy.",
    "Sports fan who never misses a Springboks match. Looking for my teammate in life!"
]

def create_sample_users(count=20):
    """Create sample users with South African data"""
    print(f"🧑‍🤝‍🧑 Creating {count} additional sample users...")

    users_created = 0

    for i in range(count):
        try:
            # Random gender
            gender = random.choice(['male', 'female'])

            # Random name
            first_name = random.choice(SOUTH_AFRICAN_NAMES[gender])
            surname = random.choice(SOUTH_AFRICAN_SURNAMES)
            full_name = f"{first_name} {surname}"

            # Random email with timestamp to ensure uniqueness
            timestamp = int(timezone.now().timestamp())
            email = f"{first_name.lower()}.{surname.lower()}.{timestamp}.{i}@heartgrid.co.za"

            # Random age between 18-45
            age = random.randint(18, 45)
            birth_year = datetime.now().year - age
            birth_month = random.randint(1, 12)
            birth_day = random.randint(1, 28)  # Safe day for all months
            date_of_birth = date(birth_year, birth_month, birth_day)

            # Skip if user already exists (shouldn't happen with timestamp)
            if User.objects.filter(email=email).exists():
                continue

            # Create user
            user = User.objects.create_user(
                email=email,
                name=full_name,
                password='password123',  # Simple password for demo
                date_of_birth=date_of_birth,
                age=age,
                auth_method='email'
            )

            # Create profile
            interested_in = 'female' if gender == 'male' else 'male'
            if random.random() < 0.1:  # 10% chance of being interested in everyone
                interested_in = 'everyone'

            profile = Profile.objects.create(
                user=user,
                bio=random.choice(BIOS),
                gender=gender,
                interested_in=interested_in,
                location=random.choice(SOUTH_AFRICAN_CITIES),
                is_complete=True,
                is_visible=True
            )

            # Set interests after profile is saved
            selected_interests = random.sample(INTERESTS, random.randint(3, 8))
            profile.interests = selected_interests
            profile.save()
            
            # Create subscription (mix of trial, premium, and expired)
            subscription_type = random.choices(
                ['trial', 'weekly', 'fortnightly', 'monthly', 'expired'],
                weights=[30, 20, 15, 25, 10]
            )[0]
            
            if subscription_type == 'trial':
                expires_at = timezone.now() + timedelta(days=random.randint(1, 3))
                features = ['basic_chat']
                status = 'active'
            elif subscription_type == 'expired':
                expires_at = timezone.now() - timedelta(days=random.randint(1, 30))
                features = ['basic_chat']
                status = 'expired'
            else:
                if subscription_type == 'weekly':
                    expires_at = timezone.now() + timedelta(days=7)
                elif subscription_type == 'fortnightly':
                    expires_at = timezone.now() + timedelta(days=14)
                else:  # monthly
                    expires_at = timezone.now() + timedelta(days=30)
                
                features = ['unlimited_likes', 'premium_filters', 'read_receipts', 'boost']
                status = 'active'
            
            Subscription.objects.create(
                user=user,
                plan=subscription_type,
                status=status,
                expires_at=expires_at,
                features=features
            )
            
            users_created += 1
            
            if users_created % 10 == 0:
                print(f"   Created {users_created} users...")
                
        except Exception as e:
            print(f"   Error creating user {i}: {e}")
            continue
    
    print(f"✅ Created {users_created} sample users")
    return users_created

def create_sample_matches(count=100):
    """Create sample matches between users"""
    print(f"💕 Creating {count} sample matches...")
    
    users = list(User.objects.all())
    if len(users) < 2:
        print("❌ Need at least 2 users to create matches")
        return 0
    
    matches_created = 0
    
    for i in range(count):
        try:
            # Pick two random users
            user1, user2 = random.sample(users, 2)
            
            # Check if match already exists
            if Match.objects.filter(
                user1=user1, user2=user2
            ).exists() or Match.objects.filter(
                user1=user2, user2=user1
            ).exists():
                continue
            
            # Create match
            match = Match.objects.create(
                user1=user1,
                user2=user2,
                matched_at=timezone.now() - timedelta(
                    days=random.randint(0, 30),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )
            )
            
            matches_created += 1
            
        except Exception as e:
            print(f"   Error creating match {i}: {e}")
            continue
    
    print(f"✅ Created {matches_created} sample matches")
    return matches_created

def create_sample_messages(count=200):
    """Create sample messages between matched users"""
    print(f"💬 Creating {count} sample messages...")
    
    matches = list(Match.objects.all())
    if not matches:
        print("❌ No matches found to create messages")
        return 0
    
    sample_messages = [
        "Hey! How's your day going?",
        "I love your profile! Tell me more about your hiking adventures.",
        "That photo from Table Mountain is amazing! 📸",
        "Are you free for coffee this weekend?",
        "I see you're into braais too! What's your secret recipe?",
        "Your taste in music is incredible! 🎵",
        "Hope you're having a great week!",
        "That sunset photo is breathtaking! Where was it taken?",
        "I'd love to hear more about your travels.",
        "You seem like such an interesting person!",
        "What's your favorite spot in Cape Town?",
        "I'm also into fitness! Do you have a favorite gym?",
        "Your bio made me smile 😊",
        "Would you like to grab dinner sometime?",
        "I love your sense of humor!"
    ]
    
    messages_created = 0
    
    for i in range(count):
        try:
            # Pick random match
            match = random.choice(matches)
            
            # Pick random sender (either user from the match)
            sender = random.choice([match.user1, match.user2])
            receiver = match.user2 if sender == match.user1 else match.user1
            
            # Create message
            message = Message.objects.create(
                sender=sender,
                receiver=receiver,
                content=random.choice(sample_messages),
                sent_at=timezone.now() - timedelta(
                    days=random.randint(0, 30),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                ),
                is_read=random.choice([True, False])
            )
            
            messages_created += 1
            
        except Exception as e:
            print(f"   Error creating message {i}: {e}")
            continue
    
    print(f"✅ Created {messages_created} sample messages")
    return messages_created

def create_sample_achievements():
    """Create sample achievements for users"""
    print("🏆 Creating sample achievements...")
    
    users = User.objects.all()
    gamification = GamificationEngine()
    achievements_created = 0
    
    for user in users:
        try:
            # Check and award achievements
            achievements = gamification.check_achievements(user)
            achievements_created += len(achievements)
            
        except Exception as e:
            print(f"   Error creating achievements for {user.name}: {e}")
            continue
    
    print(f"✅ Created {achievements_created} achievements")
    return achievements_created

def verify_data_integrity():
    """Verify that all data was created correctly"""
    print("\n🔍 Verifying data integrity...")
    
    # Count records
    user_count = User.objects.count()
    profile_count = Profile.objects.count()
    match_count = Match.objects.count()
    message_count = Message.objects.count()
    subscription_count = Subscription.objects.count()
    achievement_count = Achievement.objects.count()
    
    print(f"📊 Database Statistics:")
    print(f"   Users: {user_count}")
    print(f"   Profiles: {profile_count}")
    print(f"   Matches: {match_count}")
    print(f"   Messages: {message_count}")
    print(f"   Subscriptions: {subscription_count}")
    print(f"   Achievements: {achievement_count}")
    
    # Verify relationships
    users_without_profiles = User.objects.filter(profile__isnull=True).count()
    users_without_subscriptions = User.objects.filter(subscription__isnull=True).count()
    
    print(f"\n🔗 Relationship Integrity:")
    print(f"   Users without profiles: {users_without_profiles}")
    print(f"   Users without subscriptions: {users_without_subscriptions}")
    
    if users_without_profiles == 0 and users_without_subscriptions == 0:
        print("✅ All relationships are intact")
        return True
    else:
        print("❌ Some relationship issues found")
        return False

def main():
    """Main function to populate sample data"""
    print("🚀 Starting HeartGrid Sample Data Population...")
    print("=" * 60)

    # Check current database status
    current_users = User.objects.count()
    current_profiles = Profile.objects.count()
    current_matches = Match.objects.count()
    current_messages = Message.objects.count()

    print(f"📊 Current Database Status:")
    print(f"   Users: {current_users}")
    print(f"   Profiles: {current_profiles}")
    print(f"   Matches: {current_matches}")
    print(f"   Messages: {current_messages}")
    print()

    try:
        # Create additional sample users (smaller count since we already have data)
        users_created = create_sample_users(20)

        if users_created > 0 or current_users > 0:
            # Create matches
            create_sample_matches(50)

            # Create messages
            create_sample_messages(100)

            # Create achievements
            create_sample_achievements()

            # Verify data integrity
            integrity_ok = verify_data_integrity()

            print("\n" + "=" * 60)
            if integrity_ok:
                print("🎉 Sample data population completed successfully!")
                print("\n💡 You can now:")
                print("   • Test the matching system with realistic data")
                print("   • View user profiles with South African context")
                print("   • Test messaging between matched users")
                print("   • Explore gamification features")
                print("   • Test subscription and payment features")
            else:
                print("⚠️  Sample data created but with some integrity issues")
        else:
            print("❌ Failed to create sample users")

    except Exception as e:
        print(f"❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
