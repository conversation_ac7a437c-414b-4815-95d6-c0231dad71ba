{% extends "base.html" %}

{% block title %}My Profile - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="profile-header text-center mb-5">
                <i class="fas fa-user-edit fa-3x text-primary mb-3"></i>
                <h2 class="fw-bold">My Profile</h2>
                <p class="text-muted">Make your profile shine to attract the right matches</p>
            </div>
            
            <!-- Photo Management -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Profile Photos
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Current Photos -->
                    <div class="row g-3 mb-4">
                        {% if profile.photos %}
                            {% for photo in profile.photos %}
                                <div class="col-md-4">
                                    <div class="photo-item">
                                        <img src="{{ url_for('uploaded_file', filename=photo) }}" 
                                             alt="Profile Photo" class="img-fluid rounded">
                                        <div class="photo-overlay">
                                            <a href="{{ url_for('delete_photo', photo_filename=photo) }}" 
                                               class="btn btn-danger btn-sm"
                                               onclick="return confirm('Are you sure you want to delete this photo?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="col-12">
                                <div class="empty-photos text-center py-4">
                                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No photos uploaded yet. Add some photos to attract matches!</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Upload Form -->
                    <form method="POST" action="{{ url_for('upload_photo') }}" enctype="multipart/form-data">
                        <div class="input-group">
                            <input type="file" class="form-control" name="photo" accept="image/*" required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-upload me-1"></i>
                                Upload Photo
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Supported formats: JPG, PNG, GIF, WebP (Max: 16MB)
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Profile Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Profile Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="age" class="form-label">Age</label>
                                <input type="number" class="form-control" id="age" name="age" 
                                       min="18" max="100" value="{{ profile.age or '' }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="gender" class="form-label">Gender</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male" {{ 'selected' if profile.gender == 'male' }}>Male</option>
                                    <option value="female" {{ 'selected' if profile.gender == 'female' }}>Female</option>
                                    <option value="non-binary" {{ 'selected' if profile.gender == 'non-binary' }}>Non-binary</option>
                                    <option value="other" {{ 'selected' if profile.gender == 'other' }}>Other</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="interested_in" class="form-label">Interested In</label>
                                <select class="form-select" id="interested_in" name="interested_in">
                                    <option value="">Select Preference</option>
                                    <option value="male" {{ 'selected' if profile.interested_in == 'male' }}>Men</option>
                                    <option value="female" {{ 'selected' if profile.interested_in == 'female' }}>Women</option>
                                    <option value="everyone" {{ 'selected' if profile.interested_in == 'everyone' }}>Everyone</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       placeholder="City, State" value="{{ profile.location or '' }}">
                            </div>
                            
                            <div class="col-12">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4" 
                                          placeholder="Tell others about yourself...">{{ profile.bio or '' }}</textarea>
                                <div class="form-text">
                                    Share your interests, hobbies, and what you're looking for.
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Interests</label>
                                <div class="row g-2">
                                    {% set interest_options = [
                                        'Travel', 'Fitness', 'Music', 'Movies', 'Reading', 'Cooking',
                                        'Art', 'Sports', 'Gaming', 'Photography', 'Dancing', 'Hiking',
                                        'Yoga', 'Technology', 'Fashion', 'Food', 'Animals', 'Nature'
                                    ] %}
                                    {% for interest in interest_options %}
                                        <div class="col-md-3 col-sm-4 col-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="interests" value="{{ interest }}" id="interest_{{ loop.index }}"
                                                       {{ 'checked' if interest in (profile.interests or []) }}>
                                                <label class="form-check-label" for="interest_{{ loop.index }}">
                                                    {{ interest }}
                                                </label>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>
                                Save Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
